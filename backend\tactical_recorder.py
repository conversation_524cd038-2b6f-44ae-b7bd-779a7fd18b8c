"""
🔴 ENREGISTREUR TACTIQUE GUERRE ÉLECTRONIQUE
Enregistrement instantané avec métadonnées automatiques et analyse de signal
"""

import asyncio
import json
import logging
import numpy as np
import sounddevice as sd
import threading
import time
import wave
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import hashlib

@dataclass
class RecordingMetadata:
    """Métadonnées complètes d'enregistrement tactique"""
    # Identification
    recording_id: str
    timestamp: str
    duration: float
    
    # Paramètres radio
    frequency: int
    mode: str
    rf_gain: int
    signal_level: float
    
    # Paramètres audio
    sample_rate: int
    channels: int
    format: str
    file_size: int
    
    # Analyse signal
    signal_detected: bool
    signal_strength_avg: float
    signal_strength_max: float
    signal_strength_min: float
    noise_floor: float
    snr_estimate: float
    
    # Classification automatique
    signal_type: str  # 'voice', 'data', 'tone', 'noise', 'unknown'
    modulation_detected: str
    bandwidth_estimate: float
    
    # Géolocalisation (si disponible)
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    
    # Notes opérateur
    operator_notes: str = ""
    priority: str = "normal"  # 'low', 'normal', 'high', 'critical'
    classification: str = "unclassified"  # 'unclassified', 'restricted', 'confidential'

class TacticalRecorder:
    """Enregistreur tactique pour guerre électronique"""
    
    def __init__(self, recordings_dir: str = "recordings"):
        self.recordings_dir = recordings_dir
        self.is_recording = False
        self.current_recording = None
        self.audio_buffer = []
        self.metadata_buffer = []
        
        # Configuration audio
        self.sample_rate = 48000
        self.channels = 1
        self.dtype = np.float32
        
        # Analyse en temps réel
        self.signal_analyzer = SignalAnalyzer()
        
        # Statistiques
        self.total_recordings = 0
        self.total_duration = 0.0
        
        self.logger = logging.getLogger(__name__)
        
        # Créer le dossier d'enregistrements
        os.makedirs(recordings_dir, exist_ok=True)
        
    def start_recording(self, radio_params: Dict) -> str:
        """Démarre un enregistrement tactique instantané"""
        if self.is_recording:
            self.logger.warning("⚠️ Enregistrement déjà en cours")
            return None
        
        try:
            # Générer ID unique
            timestamp = datetime.now()
            recording_id = self._generate_recording_id(timestamp, radio_params['frequency'])
            
            # Initialiser métadonnées
            self.current_recording = RecordingMetadata(
                recording_id=recording_id,
                timestamp=timestamp.isoformat(),
                duration=0.0,
                frequency=radio_params.get('frequency', 0),
                mode=radio_params.get('mode', 'FM'),
                rf_gain=radio_params.get('rf_gain', 50),
                signal_level=radio_params.get('signal_level', -80),
                sample_rate=self.sample_rate,
                channels=self.channels,
                format='WAV',
                file_size=0,
                signal_detected=False,
                signal_strength_avg=0.0,
                signal_strength_max=-100.0,
                signal_strength_min=0.0,
                noise_floor=-90.0,
                snr_estimate=0.0,
                signal_type='unknown',
                modulation_detected='unknown',
                bandwidth_estimate=0.0
            )
            
            # Réinitialiser buffers
            self.audio_buffer = []
            self.metadata_buffer = []
            
            # Démarrer capture audio
            self.stream = sd.InputStream(
                samplerate=self.sample_rate,
                channels=self.channels,
                dtype=self.dtype,
                callback=self._audio_callback,
                blocksize=1024
            )
            
            self.stream.start()
            self.is_recording = True
            self.start_time = time.time()
            
            self.logger.info(f"🔴 Enregistrement démarré: {recording_id}")
            return recording_id
            
        except Exception as e:
            self.logger.error(f"❌ Erreur démarrage enregistrement: {e}")
            return None
    
    def stop_recording(self) -> Optional[Dict]:
        """Arrête l'enregistrement et sauvegarde avec métadonnées"""
        if not self.is_recording:
            self.logger.warning("⚠️ Aucun enregistrement en cours")
            return None
        
        try:
            # Arrêter capture
            self.is_recording = False
            self.stream.stop()
            self.stream.close()
            
            # Calculer durée
            duration = time.time() - self.start_time
            self.current_recording.duration = duration
            
            # Analyser le signal enregistré
            self._analyze_recorded_signal()
            
            # Sauvegarder fichier audio
            audio_filename = self._save_audio_file()
            
            # Sauvegarder métadonnées
            metadata_filename = self._save_metadata_file()
            
            # Statistiques
            self.total_recordings += 1
            self.total_duration += duration
            
            result = {
                'recording_id': self.current_recording.recording_id,
                'audio_file': audio_filename,
                'metadata_file': metadata_filename,
                'duration': duration,
                'metadata': asdict(self.current_recording)
            }
            
            self.logger.info(f"✅ Enregistrement terminé: {self.current_recording.recording_id} ({duration:.1f}s)")
            
            # Reset
            self.current_recording = None
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Erreur arrêt enregistrement: {e}")
            return None
    
    def _audio_callback(self, indata, frames, time_info, status):
        """Callback audio avec analyse temps réel"""
        if not self.is_recording:
            return
        
        # Stocker audio
        audio_data = indata[:, 0] if len(indata.shape) > 1 else indata
        self.audio_buffer.append(audio_data.copy())
        
        # Analyse temps réel
        analysis = self.signal_analyzer.analyze_chunk(audio_data)
        self.metadata_buffer.append({
            'timestamp': time.time(),
            'signal_level': analysis['signal_level'],
            'signal_detected': analysis['signal_detected'],
            'spectral_features': analysis.get('spectral_features', {})
        })
        
        # Mise à jour métadonnées en temps réel
        if self.current_recording:
            self.current_recording.signal_level = analysis['signal_level']
            self.current_recording.signal_detected = analysis['signal_detected']
    
    def _analyze_recorded_signal(self):
        """Analyse complète du signal enregistré"""
        if not self.metadata_buffer:
            return
        
        # Statistiques niveau signal
        signal_levels = [m['signal_level'] for m in self.metadata_buffer]
        self.current_recording.signal_strength_avg = np.mean(signal_levels)
        self.current_recording.signal_strength_max = np.max(signal_levels)
        self.current_recording.signal_strength_min = np.min(signal_levels)
        
        # Estimation plancher de bruit
        sorted_levels = sorted(signal_levels)
        self.current_recording.noise_floor = np.percentile(sorted_levels, 10)
        
        # Estimation SNR
        signal_power = np.mean([l for l in signal_levels if l > self.current_recording.noise_floor + 6])
        self.current_recording.snr_estimate = signal_power - self.current_recording.noise_floor
        
        # Classification automatique du signal
        self.current_recording.signal_type = self._classify_signal_type()
        self.current_recording.modulation_detected = self._detect_modulation()
        self.current_recording.bandwidth_estimate = self._estimate_bandwidth()
    
    def _classify_signal_type(self) -> str:
        """Classification automatique du type de signal"""
        if not self.audio_buffer:
            return 'unknown'
        
        # Analyse spectrale simple
        audio_data = np.concatenate(self.audio_buffer)
        
        # Calcul de l'énergie spectrale
        fft_data = np.fft.fft(audio_data[:8192])  # FFT sur 8k échantillons
        magnitude = np.abs(fft_data[:4096])
        
        # Caractéristiques spectrales
        spectral_centroid = np.sum(magnitude * np.arange(len(magnitude))) / np.sum(magnitude)
        spectral_spread = np.sqrt(np.sum(((np.arange(len(magnitude)) - spectral_centroid) ** 2) * magnitude) / np.sum(magnitude))
        
        # Classification basique
        if spectral_spread < 100:
            return 'tone'  # Signal tonal (CW, tonalité)
        elif spectral_centroid > 1000:
            return 'voice'  # Probable signal vocal
        elif spectral_spread > 500:
            return 'data'  # Signal numérique large bande
        else:
            return 'unknown'
    
    def _detect_modulation(self) -> str:
        """Détection automatique du type de modulation"""
        # Basé sur les paramètres radio actuels
        return self.current_recording.mode
    
    def _estimate_bandwidth(self) -> float:
        """Estimation de la bande passante du signal"""
        if not self.audio_buffer:
            return 0.0
        
        # Analyse spectrale pour estimation bande passante
        audio_data = np.concatenate(self.audio_buffer)
        fft_data = np.fft.fft(audio_data[:8192])
        magnitude = np.abs(fft_data[:4096])
        
        # Seuil à -20dB du pic
        peak_level = np.max(magnitude)
        threshold = peak_level * 0.1  # -20dB
        
        # Trouver les limites de bande
        above_threshold = magnitude > threshold
        if np.any(above_threshold):
            indices = np.where(above_threshold)[0]
            bandwidth_bins = indices[-1] - indices[0]
            bandwidth_hz = (bandwidth_bins / 4096) * (self.sample_rate / 2)
            return bandwidth_hz
        
        return 0.0
    
    def _generate_recording_id(self, timestamp: datetime, frequency: int) -> str:
        """Génère un ID unique pour l'enregistrement"""
        # Format: YYYYMMDD_HHMMSS_FREQ_HASH
        date_str = timestamp.strftime("%Y%m%d_%H%M%S")
        freq_str = f"{frequency:09d}"
        
        # Hash court pour unicité
        hash_input = f"{timestamp.isoformat()}_{frequency}_{time.time()}"
        hash_short = hashlib.md5(hash_input.encode()).hexdigest()[:6]
        
        return f"{date_str}_{freq_str}_{hash_short}"
    
    def _save_audio_file(self) -> str:
        """Sauvegarde le fichier audio WAV"""
        filename = f"{self.current_recording.recording_id}.wav"
        filepath = os.path.join(self.recordings_dir, filename)
        
        # Concaténer tous les chunks audio
        audio_data = np.concatenate(self.audio_buffer)
        
        # Normaliser et convertir en int16
        audio_normalized = audio_data / np.max(np.abs(audio_data))
        audio_int16 = (audio_normalized * 32767).astype(np.int16)
        
        # Sauvegarder WAV
        with wave.open(filepath, 'wb') as wav_file:
            wav_file.setnchannels(self.channels)
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(self.sample_rate)
            wav_file.writeframes(audio_int16.tobytes())
        
        # Mettre à jour taille fichier
        self.current_recording.file_size = os.path.getsize(filepath)
        
        return filename
    
    def _save_metadata_file(self) -> str:
        """Sauvegarde les métadonnées JSON"""
        filename = f"{self.current_recording.recording_id}_metadata.json"
        filepath = os.path.join(self.recordings_dir, filename)
        
        # Métadonnées complètes
        metadata = {
            'recording': asdict(self.current_recording),
            'analysis_timeline': self.metadata_buffer,
            'system_info': {
                'recorder_version': '1.0.0',
                'platform': os.name,
                'created_by': 'TacticalRecorder'
            }
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        return filename
    
    def get_recordings_list(self) -> List[Dict]:
        """Liste des enregistrements avec métadonnées"""
        recordings = []
        
        for filename in os.listdir(self.recordings_dir):
            if filename.endswith('_metadata.json'):
                try:
                    filepath = os.path.join(self.recordings_dir, filename)
                    with open(filepath, 'r', encoding='utf-8') as f:
                        metadata = json.load(f)
                    
                    recording_info = metadata['recording']
                    recording_info['metadata_file'] = filename
                    recording_info['audio_file'] = filename.replace('_metadata.json', '.wav')
                    
                    recordings.append(recording_info)
                    
                except Exception as e:
                    self.logger.error(f"❌ Erreur lecture métadonnées {filename}: {e}")
        
        # Trier par timestamp (plus récent en premier)
        recordings.sort(key=lambda x: x['timestamp'], reverse=True)
        return recordings
    
    def get_stats(self) -> Dict:
        """Statistiques de l'enregistreur"""
        return {
            'total_recordings': self.total_recordings,
            'total_duration': self.total_duration,
            'is_recording': self.is_recording,
            'current_recording_id': self.current_recording.recording_id if self.current_recording else None,
            'recordings_dir': self.recordings_dir
        }

class SignalAnalyzer:
    """Analyseur de signal temps réel"""
    
    def __init__(self):
        self.signal_threshold = -60  # dBFS
        
    def analyze_chunk(self, audio_data: np.ndarray) -> Dict:
        """Analyse un chunk audio en temps réel"""
        # Calcul niveau RMS
        rms_level = np.sqrt(np.mean(audio_data**2))
        db_level = 20 * np.log10(rms_level + 1e-10)
        
        # Détection signal
        signal_detected = db_level > self.signal_threshold
        
        # Analyse spectrale basique
        if len(audio_data) >= 512:
            fft_data = np.fft.fft(audio_data[:512])
            magnitude = np.abs(fft_data[:256])
            
            spectral_features = {
                'peak_frequency': np.argmax(magnitude),
                'spectral_centroid': np.sum(magnitude * np.arange(len(magnitude))) / np.sum(magnitude),
                'spectral_energy': np.sum(magnitude**2)
            }
        else:
            spectral_features = {}
        
        return {
            'signal_level': db_level,
            'signal_detected': signal_detected,
            'rms_level': rms_level,
            'spectral_features': spectral_features
        }

# Instance globale
tactical_recorder = TacticalRecorder()
