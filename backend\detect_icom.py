"""
🔍 DÉTECTION AUTOMATIQUE IC-R8600
Script pour détecter automatiquement l'IC-R8600 sur le réseau
"""

import socket
import time
import logging
from typing import Optional, List, Tuple
import subprocess
import re

class ICOMDetector:
    """Détecteur automatique IC-R8600"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.common_ports = [50001, 50002, 7300, 7301]  # Ports CI-V courants
        
    def scan_network_range(self, base_ip: str = "192.168.1", start: int = 1, end: int = 254) -> List[str]:
        """Scan du réseau pour trouver des hôtes actifs"""
        active_hosts = []
        
        print(f"🔍 Scan réseau {base_ip}.{start}-{end}...")
        
        for i in range(start, min(end + 1, 255)):
            ip = f"{base_ip}.{i}"
            
            # Test ping rapide
            try:
                result = subprocess.run(
                    ["ping", "-n", "1", "-w", "1000", ip],
                    capture_output=True,
                    text=True,
                    timeout=2
                )
                
                if result.returncode == 0:
                    active_hosts.append(ip)
                    print(f"✅ Hôte actif trouvé: {ip}")
                    
            except (subprocess.TimeoutExpired, Exception):
                pass
                
        return active_hosts
    
    def test_icom_port(self, ip: str, port: int, timeout: float = 2.0) -> bool:
        """Test si un port répond aux commandes CI-V"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(timeout)
            
            # Commande CI-V de test (lecture fréquence)
            test_command = bytes([0xFE, 0xFE, 0x96, 0xDF, 0x03, 0xFD])
            
            sock.sendto(test_command, (ip, port))
            response, _ = sock.recvfrom(1024)
            
            sock.close()
            
            # Vérifier si c'est une réponse CI-V valide
            if len(response) >= 6 and response[0:2] == bytes([0xFE, 0xFE]):
                return True
                
        except Exception:
            pass
            
        return False
    
    def detect_icom_devices(self, hosts: List[str]) -> List[Tuple[str, int]]:
        """Détecte les dispositifs ICOM sur les hôtes donnés"""
        icom_devices = []
        
        print(f"🎯 Test des ports CI-V sur {len(hosts)} hôtes...")
        
        for host in hosts:
            print(f"📡 Test {host}...")
            
            for port in self.common_ports:
                if self.test_icom_port(host, port):
                    icom_devices.append((host, port))
                    print(f"✅ IC-R8600 détecté sur {host}:{port}")
                    
        return icom_devices
    
    def get_local_network_info(self) -> List[str]:
        """Obtient les informations du réseau local"""
        networks = []
        
        try:
            # Obtenir la configuration IP Windows
            result = subprocess.run(
                ["ipconfig"],
                capture_output=True,
                text=True
            )
            
            # Extraire les adresses IP
            ip_pattern = r'Adresse IPv4.*?:\s*(\d+\.\d+\.\d+\.\d+)'
            matches = re.findall(ip_pattern, result.stdout)
            
            for ip in matches:
                if not ip.startswith('127.'):  # Ignorer localhost
                    # Extraire le réseau (ex: ************* -> 192.168.1)
                    network = '.'.join(ip.split('.')[:-1])
                    if network not in networks:
                        networks.append(network)
                        
        except Exception as e:
            print(f"❌ Erreur lecture config réseau: {e}")
            # Fallback sur les réseaux courants
            networks = ["192.168.1", "192.168.0", "10.0.0", "172.16.0"]
            
        return networks
    
    def auto_detect(self) -> Optional[Tuple[str, int]]:
        """Détection automatique complète"""
        print("🚀 DÉTECTION AUTOMATIQUE IC-R8600")
        print("=" * 50)
        
        # 1. Obtenir les réseaux locaux
        networks = self.get_local_network_info()
        print(f"🌐 Réseaux détectés: {networks}")
        
        # 2. Scanner chaque réseau
        all_hosts = []
        for network in networks:
            hosts = self.scan_network_range(network, 1, 254)
            all_hosts.extend(hosts)
            
        if not all_hosts:
            print("❌ Aucun hôte actif trouvé sur le réseau")
            return None
            
        print(f"📋 {len(all_hosts)} hôtes actifs trouvés")
        
        # 3. Tester les ports CI-V
        icom_devices = self.detect_icom_devices(all_hosts)
        
        if icom_devices:
            device = icom_devices[0]  # Prendre le premier trouvé
            print(f"🎉 IC-R8600 trouvé sur {device[0]}:{device[1]}")
            return device
        else:
            print("❌ Aucun IC-R8600 détecté")
            return None

if __name__ == "__main__":
    detector = ICOMDetector()
    result = detector.auto_detect()
    
    if result:
        ip, port = result
        print(f"\n✅ CONFIGURATION RECOMMANDÉE:")
        print(f"   IP: {ip}")
        print(f"   Port: {port}")
        print(f"\n📝 Modifiez backend/main.py ligne 71:")
        print(f'   udp_host="{ip}"')
        print(f'   udp_port={port}')
    else:
        print(f"\n❌ IC-R8600 non détecté automatiquement")
        print(f"📋 Vérifiez:")
        print(f"   1. IC-R8600 allumé")
        print(f"   2. Câble réseau connecté")
        print(f"   3. Configuration réseau IC-R8600")
