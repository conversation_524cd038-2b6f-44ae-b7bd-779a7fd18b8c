import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Radio,
  Volume2,
  VolumeX,
  Play,
  Square,
  Zap,
  Activity,
  Target,
  AlertTriangle,
  Mic,
  Settings,
  BarChart3,
  Headphones
} from 'lucide-react';
import SpectrumAnalyzer from './SpectrumAnalyzer';

/**
 * 🎯 INTERFACE TACTIQUE GUERRE ÉLECTRONIQUE
 * Contrôle instantané IC-R8600 avec audio temps réel
 */
const TacticalInterface = () => {
  // États principaux
  const [isConnected, setIsConnected] = useState(false);
  const [audioConnected, setAudioConnected] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [signalDetected, setSignalDetected] = useState(false);
  const [audioMuted, setAudioMuted] = useState(false);
  
  // États radio
  const [frequency, setFrequency] = useState(145500000);
  const [mode, setMode] = useState('FM');
  const [rfGain, setRfGain] = useState(50);
  const [signalLevel, setSignalLevel] = useState(-80);
  const [powerOn, setPowerOn] = useState(true);
  
  // États audio
  const [audioStats, setAudioStats] = useState({});
  const [spectrumData, setSpectrumData] = useState(null);
  const [signalThreshold, setSignalThreshold] = useState(-60);
  
  // WebSocket refs
  const wsRef = useRef(null);
  const audioWsRef = useRef(null);
  const audioContextRef = useRef(null);
  const audioBufferRef = useRef([]);
  
  // Modes disponibles
  const modes = ['LSB', 'USB', 'AM', 'CW', 'FM', 'WFM'];
  
  // Fréquences tactiques prédéfinies
  const tacticalFreqs = [
    { name: 'VHF Air', freq: 121500000, mode: 'AM' },
    { name: 'UHF Mil', freq: 225000000, mode: 'AM' },
    { name: 'VHF Mar', freq: 156800000, mode: 'FM' },
    { name: '2m Ham', freq: 145500000, mode: 'FM' },
    { name: '70cm Ham', freq: 433500000, mode: 'FM' },
    { name: 'PMR446', freq: 446000000, mode: 'FM' }
  ];

  // ============================================================================
  // CONNEXIONS WEBSOCKET
  // ============================================================================

  const connectWebSocket = useCallback(() => {
    // Vérifier d'abord si le backend est disponible
    fetch('http://localhost:8000/api/status')
      .then(response => {
        if (!response.ok) throw new Error('Backend non disponible');
        return connectWebSocketsInternal();
      })
      .catch(error => {
        console.log('⏳ Backend non disponible, retry dans 5s...', error.message);
        setTimeout(connectWebSocket, 5000);
      });
  }, [frequency, mode, rfGain, signalLevel, powerOn]);

  const connectWebSocketsInternal = useCallback(() => {
    try {
      // Fermer les connexions existantes
      if (wsRef.current) wsRef.current.close();
      if (audioWsRef.current) audioWsRef.current.close();

      // WebSocket principal pour contrôle
      wsRef.current = new WebSocket('ws://localhost:8000/ws');

      wsRef.current.onopen = () => {
        setIsConnected(true);
        console.log('🔗 WebSocket contrôle connecté');
      };

      wsRef.current.onmessage = (event) => {
        const data = JSON.parse(event.data);

        if (data.type === 'status') {
          const status = data.data;
          setFrequency(status.frequency || frequency);
          setMode(status.mode || mode);
          setRfGain(status.rf_gain || rfGain);
          setSignalLevel(status.rssi || signalLevel);
          setPowerOn(status.power_on !== undefined ? status.power_on : powerOn);
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('❌ Erreur WebSocket contrôle:', error);
      };

      wsRef.current.onclose = () => {
        setIsConnected(false);
        console.log('❌ WebSocket contrôle déconnecté');
        // Reconnexion automatique avec délai progressif
        setTimeout(connectWebSocket, 5000);
      };

      // WebSocket audio pour streaming RF
      audioWsRef.current = new WebSocket('ws://localhost:8000/ws/audio');

      audioWsRef.current.onopen = () => {
        setAudioConnected(true);
        console.log('🎧 WebSocket audio connecté');
        initAudioContext();
      };

      audioWsRef.current.onmessage = (event) => {
        const data = JSON.parse(event.data);

        if (data.type === 'audio') {
          handleAudioData(data);
        } else if (data.type === 'audio_stats') {
          setAudioStats(data.data);
        } else if (data.type === 'spectrum') {
          setSpectrumData(data.data);
        }
      };

      audioWsRef.current.onerror = (error) => {
        console.error('❌ Erreur WebSocket audio:', error);
      };
      
      audioWsRef.current.onclose = () => {
        setAudioConnected(false);
        console.log('❌ WebSocket audio déconnecté');
        // Reconnexion automatique avec délai
        setTimeout(() => {
          if (wsRef.current?.readyState === WebSocket.OPEN) {
            try {
              audioWsRef.current = new WebSocket('ws://localhost:8000/ws/audio');
            } catch (error) {
              console.error('❌ Erreur reconnexion WebSocket audio:', error);
            }
          }
        }, 5000);
      };

    } catch (error) {
      console.error('❌ Erreur connexion WebSocket:', error);
      // Retry après erreur
      setTimeout(connectWebSocket, 5000);
    }
  }, [frequency, mode, rfGain, signalLevel, powerOn]);

  // ============================================================================
  // AUDIO TEMPS RÉEL
  // ============================================================================

  const initAudioContext = () => {
    try {
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      console.log('🎵 Contexte audio initialisé');
    } catch (error) {
      console.error('❌ Erreur contexte audio:', error);
    }
  };

  const handleAudioData = (audioData) => {
    if (audioMuted || !audioContextRef.current) return;
    
    try {
      // Décoder les données audio base64
      const audioBytes = atob(audioData.data);
      const audioArray = new Int16Array(audioBytes.length / 2);
      
      for (let i = 0; i < audioArray.length; i++) {
        audioArray[i] = (audioBytes.charCodeAt(i * 2) | (audioBytes.charCodeAt(i * 2 + 1) << 8));
      }
      
      // Convertir en Float32Array pour Web Audio API
      const floatArray = new Float32Array(audioArray.length);
      for (let i = 0; i < audioArray.length; i++) {
        floatArray[i] = audioArray[i] / 32768.0;
      }
      
      // Créer buffer audio et jouer
      const audioBuffer = audioContextRef.current.createBuffer(1, floatArray.length, audioData.sample_rate);
      audioBuffer.getChannelData(0).set(floatArray);
      
      const source = audioContextRef.current.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(audioContextRef.current.destination);
      source.start();
      
      // Mise à jour détection signal
      setSignalLevel(audioData.signal_level);
      setSignalDetected(audioData.signal_detected);
      
    } catch (error) {
      console.error('❌ Erreur traitement audio:', error);
    }
  };

  // ============================================================================
  // CONTRÔLES TACTIQUES
  // ============================================================================

  const sendCommand = (action, data) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({ action, data }));
    }
  };

  const setFrequencyFast = (freq) => {
    setFrequency(freq);
    sendCommand('frequency', { frequency: freq });
  };

  const setModeFast = (newMode) => {
    setMode(newMode);
    sendCommand('mode', { mode: newMode });
  };

  const setRfGainFast = (gain) => {
    setRfGain(gain);
    sendCommand('rf_gain', { gain });
  };

  const togglePower = () => {
    const newState = !powerOn;
    setPowerOn(newState);
    sendCommand('power', { state: newState });
  };

  const quickFreqChange = (delta) => {
    const newFreq = frequency + delta;
    if (newFreq > 0 && newFreq < 3000000000) {
      setFrequencyFast(newFreq);
    }
  };

  const startRecording = async () => {
    try {
      const response = await fetch('http://localhost:8000/recording/start', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        const data = await response.json();
        setIsRecording(true);
        console.log('🔴 Enregistrement démarré:', data.recording_id);
      }
    } catch (error) {
      console.error('❌ Erreur démarrage enregistrement:', error);
    }
  };

  const stopRecording = async () => {
    try {
      const response = await fetch('http://localhost:8000/recording/stop', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        const data = await response.json();
        setIsRecording(false);
        console.log('⏹️ Enregistrement arrêté:', data.recording_data);
      }
    } catch (error) {
      console.error('❌ Erreur arrêt enregistrement:', error);
    }
  };

  // ============================================================================
  // RACCOURCIS CLAVIER TACTIQUES AVANCÉS
  // ============================================================================

  useEffect(() => {
    const handleKeyPress = (event) => {
      // Ignorer si focus sur input
      if (event.target.tagName === 'INPUT') return;

      const isShift = event.shiftKey;
      const isCtrl = event.ctrlKey;

      switch (event.key.toLowerCase()) {
        // CONTRÔLES FRÉQUENCE
        case 'arrowup':
          event.preventDefault();
          if (isShift) {
            quickFreqChange(100000); // +100kHz (Shift)
          } else if (isCtrl) {
            quickFreqChange(1000); // +1kHz (Ctrl)
          } else {
            quickFreqChange(25000); // +25kHz (normal)
          }
          break;

        case 'arrowdown':
          event.preventDefault();
          if (isShift) {
            quickFreqChange(-100000); // -100kHz (Shift)
          } else if (isCtrl) {
            quickFreqChange(-1000); // -1kHz (Ctrl)
          } else {
            quickFreqChange(-25000); // -25kHz (normal)
          }
          break;

        case 'arrowright':
          event.preventDefault();
          if (isShift) {
            quickFreqChange(10000000); // +10MHz (Shift)
          } else {
            quickFreqChange(1000000); // +1MHz (normal)
          }
          break;

        case 'arrowleft':
          event.preventDefault();
          if (isShift) {
            quickFreqChange(-10000000); // -10MHz (Shift)
          } else {
            quickFreqChange(-1000000); // -1MHz (normal)
          }
          break;

        // CONTRÔLES SYSTÈME
        case ' ':
          event.preventDefault();
          togglePower();
          break;

        case 'm':
          event.preventDefault();
          setAudioMuted(!audioMuted);
          break;

        case 'r':
          event.preventDefault();
          if (isRecording) {
            stopRecording();
          } else {
            startRecording();
          }
          break;

        // CONTRÔLES RF GAIN
        case '+':
        case '=':
          event.preventDefault();
          const newGainUp = Math.min(100, rfGain + (isShift ? 10 : 5));
          setRfGainFast(newGainUp);
          break;

        case '-':
          event.preventDefault();
          const newGainDown = Math.max(0, rfGain - (isShift ? 10 : 5));
          setRfGainFast(newGainDown);
          break;

        // CHANGEMENT MODE
        case 'q':
          event.preventDefault();
          const currentModeIndex = modes.indexOf(mode);
          const nextModeIndex = (currentModeIndex + 1) % modes.length;
          setModeFast(modes[nextModeIndex]);
          break;

        // FRÉQUENCES TACTIQUES (1-6)
        case '1':
        case '2':
        case '3':
        case '4':
        case '5':
        case '6':
          event.preventDefault();
          const freqIndex = parseInt(event.key) - 1;
          if (tacticalFreqs[freqIndex]) {
            const tactical = tacticalFreqs[freqIndex];
            setFrequencyFast(tactical.freq);
            setModeFast(tactical.mode);
          }
          break;

        // CONTRÔLES AUDIO
        case 's':
          event.preventDefault();
          // Demander spectre audio
          if (audioWsRef.current?.readyState === WebSocket.OPEN) {
            audioWsRef.current.send(JSON.stringify({ type: 'get_spectrum' }));
          }
          break;

        case 't':
          event.preventDefault();
          // Ajuster seuil détection signal
          const newThreshold = isShift ? signalThreshold + 5 : signalThreshold - 5;
          setSignalThreshold(Math.max(-100, Math.min(-20, newThreshold)));
          if (audioWsRef.current?.readyState === WebSocket.OPEN) {
            audioWsRef.current.send(JSON.stringify({
              type: 'set_threshold',
              threshold: newThreshold
            }));
          }
          break;

        // AIDE
        case 'h':
        case '?':
          event.preventDefault();
          alert(`🎯 RACCOURCIS CLAVIER TACTIQUES:

FRÉQUENCE:
↑↓ : ±25kHz | Shift+↑↓ : ±100kHz | Ctrl+↑↓ : ±1kHz
←→ : ±1MHz | Shift+←→ : ±10MHz

CONTRÔLES:
ESPACE : Power ON/OFF
M : Mute/Unmute audio
R : Start/Stop recording
Q : Cycle modes (LSB→USB→AM→CW→FM→WFM)

RF GAIN:
+ : +5% | Shift++ : +10%
- : -5% | Shift+- : -10%

TACTIQUES:
1-6 : Fréquences prédéfinies

AUDIO:
S : Demander spectre
T : Seuil signal -5dB | Shift+T : +5dB

AIDE:
H ou ? : Cette aide`);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [frequency, audioMuted, isRecording, powerOn, mode, rfGain, signalThreshold, modes, tacticalFreqs]);

  // ============================================================================
  // CONNEXION INITIALE
  // ============================================================================

  useEffect(() => {
    // Délai initial pour laisser le backend se stabiliser
    const initTimeout = setTimeout(() => {
      connectWebSocket();
    }, 1000); // 1 seconde de délai

    return () => {
      clearTimeout(initTimeout);
      if (wsRef.current) wsRef.current.close();
      if (audioWsRef.current) audioWsRef.current.close();
      if (audioContextRef.current) audioContextRef.current.close();
    };
  }, [connectWebSocket]);

  // ============================================================================
  // STYLES TACTIQUES
  // ============================================================================

  const styles = {
    container: {
      backgroundColor: '#0a0a0a',
      color: '#00ff00',
      fontFamily: 'Courier New, monospace',
      minHeight: '100vh',
      padding: '10px',
      overflow: 'hidden'
    },
    header: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      borderBottom: '2px solid #00ff00',
      paddingBottom: '10px',
      marginBottom: '20px'
    },
    title: {
      fontSize: '24px',
      fontWeight: 'bold',
      display: 'flex',
      alignItems: 'center',
      gap: '10px'
    },
    status: {
      display: 'flex',
      gap: '20px',
      alignItems: 'center'
    },
    statusIndicator: {
      display: 'flex',
      alignItems: 'center',
      gap: '5px',
      padding: '5px 10px',
      border: '1px solid',
      borderRadius: '3px'
    },
    connected: {
      borderColor: '#00ff00',
      backgroundColor: '#001100'
    },
    disconnected: {
      borderColor: '#ff0000',
      backgroundColor: '#110000',
      color: '#ff0000'
    },
    mainGrid: {
      display: 'grid',
      gridTemplateColumns: '1fr 1fr 1fr',
      gap: '20px',
      height: 'calc(100vh - 120px)'
    },
    panel: {
      border: '2px solid #00ff00',
      borderRadius: '5px',
      padding: '15px',
      backgroundColor: '#001100'
    },
    panelTitle: {
      fontSize: '18px',
      fontWeight: 'bold',
      marginBottom: '15px',
      display: 'flex',
      alignItems: 'center',
      gap: '10px'
    }
  };

  return (
    <div style={styles.container}>
      {/* En-tête tactique */}
      <div style={styles.header}>
        <div style={styles.title}>
          <Target size={32} />
          ICOM IC-R8600 - STATION GUERRE ÉLECTRONIQUE
        </div>
        
        <div style={styles.status}>
          <div style={{
            ...styles.statusIndicator,
            ...(isConnected ? styles.connected : styles.disconnected)
          }}>
            <Radio size={16} />
            {isConnected ? 'CONNECTÉ' : 'DÉCONNECTÉ'}
          </div>
          
          <div style={{
            ...styles.statusIndicator,
            ...(audioConnected ? styles.connected : styles.disconnected)
          }}>
            <Headphones size={16} />
            {audioConnected ? 'AUDIO OK' : 'AUDIO OFF'}
          </div>
          
          {signalDetected && (
            <div style={{
              ...styles.statusIndicator,
              borderColor: '#ffff00',
              backgroundColor: '#111100',
              color: '#ffff00'
            }}>
              <AlertTriangle size={16} />
              SIGNAL DÉTECTÉ
            </div>
          )}
        </div>
      </div>

      {/* Interface principale */}
      <div style={styles.mainGrid}>
        {/* Panel de contrôle principal */}
        <div style={styles.panel}>
          <div style={styles.panelTitle}>
            <Settings size={20} />
            CONTRÔLES PRINCIPAUX
          </div>
          
          {/* Fréquence */}
          <div style={{ marginBottom: '15px' }}>
            <label style={{ display: 'block', marginBottom: '5px' }}>
              FRÉQUENCE (Hz)
            </label>
            <input
              type="number"
              value={frequency}
              onChange={(e) => setFrequencyFast(parseInt(e.target.value))}
              style={{
                width: '100%',
                padding: '8px',
                backgroundColor: '#000',
                border: '1px solid #00ff00',
                color: '#00ff00',
                fontFamily: 'Courier New, monospace'
              }}
            />
            <div style={{ fontSize: '12px', marginTop: '5px' }}>
              {(frequency / 1000000).toFixed(3)} MHz
            </div>
          </div>

          {/* Mode */}
          <div style={{ marginBottom: '15px' }}>
            <label style={{ display: 'block', marginBottom: '5px' }}>
              MODE
            </label>
            <select
              value={mode}
              onChange={(e) => setModeFast(e.target.value)}
              style={{
                width: '100%',
                padding: '8px',
                backgroundColor: '#000',
                border: '1px solid #00ff00',
                color: '#00ff00',
                fontFamily: 'Courier New, monospace'
              }}
            >
              {modes.map(m => (
                <option key={m} value={m}>{m}</option>
              ))}
            </select>
          </div>

          {/* RF Gain */}
          <div style={{ marginBottom: '15px' }}>
            <label style={{ display: 'block', marginBottom: '5px' }}>
              RF GAIN: {rfGain}%
            </label>
            <input
              type="range"
              min="0"
              max="100"
              value={rfGain}
              onChange={(e) => setRfGainFast(parseInt(e.target.value))}
              style={{ width: '100%' }}
            />
          </div>

          {/* Contrôles rapides */}
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '10px' }}>
            <button
              onClick={togglePower}
              style={{
                padding: '10px',
                backgroundColor: powerOn ? '#001100' : '#110000',
                border: `2px solid ${powerOn ? '#00ff00' : '#ff0000'}`,
                color: powerOn ? '#00ff00' : '#ff0000',
                fontFamily: 'Courier New, monospace',
                cursor: 'pointer'
              }}
            >
              {powerOn ? 'ON' : 'OFF'}
            </button>
            
            <button
              onClick={() => setAudioMuted(!audioMuted)}
              style={{
                padding: '10px',
                backgroundColor: audioMuted ? '#110000' : '#001100',
                border: `2px solid ${audioMuted ? '#ff0000' : '#00ff00'}`,
                color: audioMuted ? '#ff0000' : '#00ff00',
                fontFamily: 'Courier New, monospace',
                cursor: 'pointer'
              }}
            >
              {audioMuted ? <VolumeX size={16} /> : <Volume2 size={16} />}
            </button>
          </div>
        </div>

        {/* Panel fréquences tactiques */}
        <div style={styles.panel}>
          <div style={styles.panelTitle}>
            <Target size={20} />
            FRÉQUENCES TACTIQUES
          </div>
          
          {tacticalFreqs.map((tactical, index) => (
            <button
              key={index}
              onClick={() => {
                setFrequencyFast(tactical.freq);
                setModeFast(tactical.mode);
              }}
              style={{
                width: '100%',
                padding: '8px',
                marginBottom: '5px',
                backgroundColor: frequency === tactical.freq ? '#001100' : '#000',
                border: `1px solid ${frequency === tactical.freq ? '#ffff00' : '#00ff00'}`,
                color: frequency === tactical.freq ? '#ffff00' : '#00ff00',
                fontFamily: 'Courier New, monospace',
                cursor: 'pointer',
                textAlign: 'left'
              }}
            >
              {index + 1}. {tactical.name} - {(tactical.freq / 1000000).toFixed(3)} MHz ({tactical.mode})
            </button>
          ))}
          
          <div style={{ marginTop: '20px', fontSize: '12px' }}>
            <div>RACCOURCIS CLAVIER:</div>
            <div>↑↓ : ±25kHz | ←→ : ±1MHz</div>
            <div>ESPACE : Power | M : Mute</div>
            <div>R : Record | 1-6 : Tactiques</div>
          </div>
        </div>

        {/* Panel audio et enregistrement */}
        <div style={styles.panel}>
          <div style={styles.panelTitle}>
            <Activity size={20} />
            AUDIO & ANALYSE
          </div>
          
          {/* Niveau signal */}
          <div style={{ marginBottom: '15px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <span>SIGNAL:</span>
              <span>{signalLevel.toFixed(1)} dBFS</span>
            </div>
            <div style={{
              width: '100%',
              height: '20px',
              backgroundColor: '#000',
              border: '1px solid #00ff00',
              position: 'relative',
              marginTop: '5px'
            }}>
              <div style={{
                width: `${Math.max(0, Math.min(100, (signalLevel + 100)))}%`,
                height: '100%',
                backgroundColor: signalDetected ? '#ffff00' : '#00ff00'
              }} />
            </div>
          </div>

          {/* Contrôles enregistrement */}
          <div style={{ marginBottom: '15px' }}>
            <button
              onClick={isRecording ? stopRecording : startRecording}
              style={{
                width: '100%',
                padding: '15px',
                backgroundColor: isRecording ? '#110000' : '#001100',
                border: `2px solid ${isRecording ? '#ff0000' : '#00ff00'}`,
                color: isRecording ? '#ff0000' : '#00ff00',
                fontFamily: 'Courier New, monospace',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '10px'
              }}
            >
              {isRecording ? <Square size={20} /> : <Play size={20} />}
              {isRecording ? 'ARRÊTER ENREG.' : 'DÉMARRER ENREG.'}
            </button>
          </div>

          {/* Statistiques audio */}
          {audioStats && (
            <div style={{ fontSize: '12px' }}>
              <div>Clients: {audioStats.clients_connected || 0}</div>
              <div>Latence: {(audioStats.latency_ms || 0).toFixed(1)}ms</div>
              <div>Paquets: {audioStats.packets_sent || 0}</div>
              <div>Streaming: {audioStats.is_streaming ? 'OUI' : 'NON'}</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TacticalInterface;
