"""
🎧 STREAMING AUDIO TEMPS RÉEL - GUERRE ÉLECTRONIQUE
Streaming audio RF ultra-rapide via WebSocket pour interception tactique
"""

import asyncio
import json
import logging
import numpy as np
import sounddevice as sd
import threading
import time
from typing import Dict, List, Optional, Set
from dataclasses import dataclass
from collections import deque
import base64

@dataclass
class AudioConfig:
    """Configuration audio optimisée pour guerre électronique"""
    sample_rate: int = 48000  # Fréquence d'échantillonnage
    channels: int = 1  # Mono pour RF
    dtype: str = 'float32'  # Précision optimale
    blocksize: int = 1024  # Buffer ultra-court pour latence minimale
    latency: str = 'low'  # Latence minimale
    device: Optional[int] = None  # Auto-détection
    
class AudioStreamer:
    """Streamer audio RF temps réel pour guerre électronique"""
    
    def __init__(self, config: AudioConfig = None):
        self.config = config or AudioConfig()
        self.is_streaming = False
        self.websocket_clients: Set = set()
        self.audio_buffer = deque(maxlen=100)  # Buffer circulaire
        self.stream = None
        self.logger = logging.getLogger(__name__)
        
        # Statistiques temps réel
        self.stats = {
            'packets_sent': 0,
            'bytes_sent': 0,
            'latency_ms': 0,
            'clients_connected': 0,
            'signal_level': 0,
            'last_update': time.time()
        }
        
        # Détection de signaux
        self.signal_threshold = -60  # dBFS
        self.signal_detected = False
        self.signal_history = deque(maxlen=50)  # Historique niveau signal
        
    def add_websocket_client(self, websocket):
        """Ajoute un client WebSocket"""
        self.websocket_clients.add(websocket)
        self.stats['clients_connected'] = len(self.websocket_clients)
        self.logger.info(f"📡 Client WebSocket connecté. Total: {len(self.websocket_clients)}")
    
    def remove_websocket_client(self, websocket):
        """Supprime un client WebSocket"""
        self.websocket_clients.discard(websocket)
        self.stats['clients_connected'] = len(self.websocket_clients)
        self.logger.info(f"📡 Client WebSocket déconnecté. Total: {len(self.websocket_clients)}")
    
    def start_streaming(self) -> bool:
        """Démarre le streaming audio RF"""
        if self.is_streaming:
            return True
            
        try:
            self.logger.info("🎧 Démarrage streaming audio RF...")
            
            # Configuration du stream audio
            self.stream = sd.InputStream(
                samplerate=self.config.sample_rate,
                channels=self.config.channels,
                dtype=self.config.dtype,
                blocksize=self.config.blocksize,
                latency=self.config.latency,
                device=self.config.device,
                callback=self._audio_callback
            )
            
            self.stream.start()
            self.is_streaming = True
            
            # Démarrer la tâche de diffusion
            asyncio.create_task(self._broadcast_audio())
            
            self.logger.info("✅ Streaming audio RF démarré")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Erreur démarrage streaming: {e}")
            return False
    
    def stop_streaming(self):
        """Arrête le streaming audio"""
        if not self.is_streaming:
            return
            
        self.is_streaming = False
        
        if self.stream:
            self.stream.stop()
            self.stream.close()
            self.stream = None
        
        self.logger.info("🛑 Streaming audio arrêté")
    
    def _audio_callback(self, indata, frames, time_info, status):
        """Callback audio ultra-rapide"""
        if status:
            self.logger.warning(f"⚠️ Audio status: {status}")
        
        # Conversion et analyse du signal
        audio_data = indata[:, 0] if len(indata.shape) > 1 else indata
        
        # Calcul niveau signal (RMS)
        rms_level = np.sqrt(np.mean(audio_data**2))
        db_level = 20 * np.log10(rms_level + 1e-10)  # Éviter log(0)
        
        # Mise à jour statistiques
        self.stats['signal_level'] = db_level
        self.signal_history.append(db_level)
        
        # Détection de signal d'intérêt
        self.signal_detected = db_level > self.signal_threshold
        
        # Ajout au buffer pour diffusion
        if self.websocket_clients:
            # Encodage optimisé pour transmission
            audio_bytes = (audio_data * 32767).astype(np.int16).tobytes()
            
            packet = {
                'type': 'audio',
                'data': base64.b64encode(audio_bytes).decode('utf-8'),
                'timestamp': time.time(),
                'sample_rate': self.config.sample_rate,
                'signal_level': db_level,
                'signal_detected': self.signal_detected,
                'frames': frames
            }
            
            self.audio_buffer.append(packet)
    
    async def _broadcast_audio(self):
        """Diffusion audio vers tous les clients WebSocket"""
        while self.is_streaming:
            if self.audio_buffer and self.websocket_clients:
                try:
                    # Récupérer le paquet audio le plus récent
                    packet = self.audio_buffer.popleft()
                    packet_json = json.dumps(packet)
                    
                    # Diffuser vers tous les clients connectés
                    disconnected_clients = set()
                    
                    for client in self.websocket_clients:
                        try:
                            await client.send_text(packet_json)
                            self.stats['packets_sent'] += 1
                            self.stats['bytes_sent'] += len(packet_json)
                        except Exception as e:
                            self.logger.warning(f"⚠️ Erreur envoi client: {e}")
                            disconnected_clients.add(client)
                    
                    # Nettoyer les clients déconnectés
                    for client in disconnected_clients:
                        self.remove_websocket_client(client)
                    
                    # Calcul latence approximative
                    current_time = time.time()
                    self.stats['latency_ms'] = (current_time - packet['timestamp']) * 1000
                    self.stats['last_update'] = current_time
                    
                except Exception as e:
                    self.logger.error(f"❌ Erreur diffusion audio: {e}")
            
            # Attente ultra-courte pour latence minimale
            await asyncio.sleep(0.001)  # 1ms
    
    def get_audio_devices(self) -> List[Dict]:
        """Liste des périphériques audio disponibles"""
        try:
            devices = sd.query_devices()
            audio_devices = []
            
            for i, device in enumerate(devices):
                if device['max_input_channels'] > 0:  # Périphériques d'entrée
                    audio_devices.append({
                        'id': i,
                        'name': device['name'],
                        'channels': device['max_input_channels'],
                        'sample_rate': device['default_samplerate'],
                        'hostapi': device['hostapi']
                    })
            
            return audio_devices
            
        except Exception as e:
            self.logger.error(f"❌ Erreur liste périphériques: {e}")
            return []
    
    def set_audio_device(self, device_id: int) -> bool:
        """Change le périphérique audio"""
        try:
            # Arrêter le streaming actuel
            was_streaming = self.is_streaming
            if was_streaming:
                self.stop_streaming()
            
            # Changer le périphérique
            self.config.device = device_id
            
            # Redémarrer si nécessaire
            if was_streaming:
                return self.start_streaming()
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Erreur changement périphérique: {e}")
            return False
    
    def set_signal_threshold(self, threshold_db: float):
        """Définit le seuil de détection de signal"""
        self.signal_threshold = threshold_db
        self.logger.info(f"🎯 Seuil détection signal: {threshold_db} dBFS")
    
    def get_stats(self) -> Dict:
        """Retourne les statistiques de streaming"""
        return {
            **self.stats,
            'is_streaming': self.is_streaming,
            'buffer_size': len(self.audio_buffer),
            'signal_threshold': self.signal_threshold,
            'avg_signal_level': np.mean(list(self.signal_history)) if self.signal_history else -100
        }
    
    def get_spectrum_data(self, fft_size: int = 1024) -> Optional[Dict]:
        """Calcule le spectre audio pour affichage"""
        if not self.audio_buffer:
            return None
        
        try:
            # Récupérer les dernières données audio
            recent_packets = list(self.audio_buffer)[-10:]  # 10 derniers paquets
            
            if not recent_packets:
                return None
            
            # Reconstituer le signal audio
            audio_data = []
            for packet in recent_packets:
                decoded_bytes = base64.b64decode(packet['data'])
                audio_samples = np.frombuffer(decoded_bytes, dtype=np.int16).astype(np.float32) / 32767.0
                audio_data.extend(audio_samples)
            
            if len(audio_data) < fft_size:
                return None
            
            # Calcul FFT
            audio_array = np.array(audio_data[-fft_size:])
            fft_data = np.fft.fft(audio_array)
            magnitude = np.abs(fft_data[:fft_size//2])
            
            # Conversion en dB
            magnitude_db = 20 * np.log10(magnitude + 1e-10)
            
            # Fréquences correspondantes
            freqs = np.fft.fftfreq(fft_size, 1/self.config.sample_rate)[:fft_size//2]
            
            return {
                'frequencies': freqs.tolist(),
                'magnitude': magnitude_db.tolist(),
                'timestamp': time.time(),
                'fft_size': fft_size
            }
            
        except Exception as e:
            self.logger.error(f"❌ Erreur calcul spectre: {e}")
            return None

# Instance globale
audio_streamer = AudioStreamer()
