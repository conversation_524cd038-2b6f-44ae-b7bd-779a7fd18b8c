"""
Module de communication CI-V pour ICOM IC-R8600
Basé sur le CI-V Reference Guide officiel
"""

import serial
import socket
import time
import logging
from typing import Optional, Dict, Any, List
from dataclasses import dataclass

# Configuration CI-V
ICOM_ADDRESS = 0x96  # IC-R8600
CONTROLLER_ADDRESS = 0xDF  # Adresse contrôleur (DFh)
PREAMBLE = [0xFE, 0xFE]
POSTAMBLE = 0xFD

# Modes de modulation
MODES = {
    'LSB': 0x00, 'USB': 0x01, 'AM': 0x02, 'CW': 0x03,
    'FM': 0x05, 'WFM': 0x06, 'CWR': 0x07, 'RTTY': 0x08,
    'RTTYR': 0x09, 'PSK': 0x12, 'PSKR': 0x13
}

@dataclass
class RadioStatus:
    frequency: int = 0
    mode: str = "FM"
    rssi: int = 0
    power_on: bool = False
    rf_gain: int = 128
    filter_width: int = 0

class ICOMHandler:
    def __init__(self, port: str = "COM6", baudrate: int = 19200):
        """Initialise la communication avec l'IC-R8600"""
        self.port = port
        self.baudrate = baudrate
        self.serial: Optional[serial.Serial] = None
        self.connected = False
        
        # Configuration logger
        self.logger = logging.getLogger('icom_handler')
        
        # Constantes CI-V
        self.CONTROLLER_ADDRESS = 0xDF  # Adresse du contrôleur (ordinateur)
        self.RECEIVER_ADDRESS = 0x96    # Adresse par défaut de l'IC-R8600 (96h/150)
        
        # Commandes CI-V
        self.CMD_READ_FREQ = 0x03
        self.CMD_READ_MODE = 0x04
        self.CMD_SET_FREQ = 0x05
        self.CMD_SET_MODE = 0x06
        
        # Timeout plus long pour la stabilité
        self.TIMEOUT = 5.0
        
        # Tentative de connexion
        self.connect()
    
    def connect(self) -> bool:
        """Établit la connexion série avec l'IC-R8600"""
        try:
            self.logger.info(f"📡 Connexion série sur {self.port} à {self.baudrate} bauds...")
            
            self.serial = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=self.TIMEOUT,
                write_timeout=self.TIMEOUT
            )
            
            if self.serial.is_open:
                self.connected = True
                self.logger.info(f"✅ Connexion série établie sur {self.port}")
                
                # Test de communication
                if self.test_communication():
                    self.logger.info("✅ Communication CI-V vérifiée")
                    return True
                else:
                    self.logger.warning("⚠️ Pas de réponse CI-V")
                    return False
                    
        except Exception as e:
            self.logger.error(f"❌ Erreur connexion série: {str(e)}")
            self.connected = False
            
        return False
    
    def test_communication(self) -> bool:
        """Teste la communication CI-V"""
        try:
            # Commande de lecture fréquence
            command = bytes([0xFE, 0xFE, self.RECEIVER_ADDRESS, self.CONTROLLER_ADDRESS, 
                           self.CMD_READ_FREQ, 0xFD])
            
            response = self.send_command(command)
            
            if response and len(response) >= 11:
                self.logger.info(f"✅ Réponse CI-V reçue: {response.hex().upper()}")
                return True
                
        except Exception as e:
            self.logger.error(f"❌ Erreur test communication: {str(e)}")
            
        return False
    
    def disconnect(self):
        """Ferme la connexion série"""
        if self.serial and self.serial.is_open:
            self.serial.close()
            self.connected = False
            self.logger.info("Déconnexion série")
    
    def send_command(self, command: bytes) -> Optional[bytes]:
        """Envoie une commande CI-V et retourne la réponse"""
        if not self.connected or not self.serial:
            self.logger.error("❌ Non connecté")
            return None
            
        try:
            # Reset des buffers
            self.serial.reset_input_buffer()
            self.serial.reset_output_buffer()
            
            # Envoi commande
            self.serial.write(command)
            self.logger.debug(f"📡 Envoyé: {command.hex().upper()}")
            
            # Attente stabilisation
            time.sleep(0.1)
            
            # Lecture réponse
            response = self.serial.read(50)
            
            if response:
                self.logger.debug(f"📡 Reçu: {response.hex().upper()}")
                return response
            else:
                self.logger.warning("⚠️ Pas de réponse")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Erreur envoi: {str(e)}")
            return None
    
    def read_frequency(self) -> Optional[int]:
        """Lit la fréquence actuelle"""
        command = bytes([0xFE, 0xFE, self.RECEIVER_ADDRESS, self.CONTROLLER_ADDRESS, 
                        self.CMD_READ_FREQ, 0xFD])
        
        response = self.send_command(command)
        
        if response and len(response) >= 11:
            # Extraction fréquence des octets 6-10
            freq_bytes = response[6:11]
            frequency = 0
            
            # Conversion BCD vers int
            for i, byte in enumerate(freq_bytes):
                frequency += (byte & 0x0F) * (10 ** (2*i))
                frequency += ((byte >> 4) & 0x0F) * (10 ** (2*i + 1))
                
            self.logger.info(f"📻 Fréquence: {frequency} Hz")
            return frequency
            
        return None
    
    def read_mode(self) -> Optional[Dict[str, Any]]:
        """Lit le mode de réception actuel"""
        command = bytes([0xFE, 0xFE, self.RECEIVER_ADDRESS, self.CONTROLLER_ADDRESS,
                        self.CMD_READ_MODE, 0xFD])
        
        response = self.send_command(command)
        
        if response and len(response) >= 8:
            mode_byte = response[6]
            filter_byte = response[7]
            
            # Conversion mode
            modes = {
                0x00: "LSB",
                0x01: "USB",
                0x02: "AM",
                0x03: "CW",
                0x05: "FM",
                0x07: "CW-R",
                0x08: "RTTY-R",
                0x0A: "WFM"
            }
            
            mode = modes.get(mode_byte, "Unknown")
            
            self.logger.info(f"📻 Mode: {mode}, Filtre: {filter_byte}")
            return {"mode": mode, "filter": filter_byte}
            
        return None
    
    def set_frequency(self, frequency: int) -> bool:
        """Configure la fréquence"""
        if not 0 <= frequency <= 3000000000:
            self.logger.error("❌ Fréquence hors limites")
            return False
            
        # Conversion int vers BCD
        freq_bcd = []
        freq_str = f"{frequency:010d}"  # 10 chiffres padded avec des 0
        
        for i in range(0, 10, 2):
            byte = (int(freq_str[i]) << 4) | int(freq_str[i+1])
            freq_bcd.append(byte)
            
        command = bytes([0xFE, 0xFE, self.RECEIVER_ADDRESS, self.CONTROLLER_ADDRESS,
                        self.CMD_SET_FREQ] + freq_bcd + [0xFD])
                        
        response = self.send_command(command)
        
        if response:
            self.logger.info(f"📻 Fréquence configurée: {frequency} Hz")
            return True
            
        return False
    
    def set_mode(self, mode: str, filter_width: int = 1) -> bool:
        """Configure le mode de réception"""
        # Conversion mode
        modes = {
            "LSB": 0x00,
            "USB": 0x01,
            "AM": 0x02,
            "CW": 0x03,
            "FM": 0x05,
            "CW-R": 0x07,
            "RTTY-R": 0x08,
            "WFM": 0x0A
        }
        
        if mode not in modes:
            self.logger.error(f"❌ Mode invalide: {mode}")
            return False
            
        if not 1 <= filter_width <= 3:
            self.logger.error(f"❌ Filtre invalide: {filter_width}")
            return False
            
        command = bytes([0xFE, 0xFE, self.RECEIVER_ADDRESS, self.CONTROLLER_ADDRESS,
                        self.CMD_SET_MODE, modes[mode], filter_width, 0xFD])
                        
        response = self.send_command(command)
        
        if response:
            self.logger.info(f"📻 Mode configuré: {mode}, Filtre: {filter_width}")
            return True
            
        return False
    
    def get_status(self) -> Dict[str, Any]:
        """Retourne l'état complet du récepteur"""
        status = {
            "connected": self.connected,
            "frequency": None,
            "mode": None,
            "filter": None
        }
        
        if self.connected:
            # Lecture fréquence
            frequency = self.read_frequency()
            if frequency:
                status["frequency"] = frequency
                
            # Lecture mode
            mode_info = self.read_mode()
            if mode_info:
                status["mode"] = mode_info["mode"]
                status["filter"] = mode_info["filter"]
                
        return status
    
    def check_udp_connection(self, udp_host, udp_port):
        """Teste la connectivité UDP"""
        import socket
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(2.0)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 65536)  # Buffer de 64KB
            sock.sendto(b"PING", (udp_host, udp_port))
            try:
                sock.recv(1024)
            except Exception:
                pass  # On ignore l'absence de réponse
            sock.close()
            return True
        except Exception as e:
            self.logger.error(f"Erreur UDP: {e}")
            return False
