@echo off
echo.
echo ========================================
echo 🔍 TEST CONNEXION RESEAU IC-R8600
echo ========================================
echo.

echo Ce script va tester la connexion reseau avec votre IC-R8600
echo Assurez-vous que:
echo   - Le cable RJ45 est connecte
echo   - L'IC-R8600 est allume
echo   - La configuration reseau est correcte
echo.

pause

echo.
echo 🧪 Execution des tests automatiques...
echo.

cd backend
python test_network_auto.py

echo.
echo ========================================
echo 📋 TESTS TERMINES
echo ========================================
echo.

if %errorlevel% equ 0 (
    echo ✅ SUCCES: Connexion reseau fonctionnelle !
    echo.
    echo Vous pouvez maintenant demarrer le serveur avec:
    echo   - start.bat (mode standard)
    echo   - start_ultra_fast.bat (mode haute performance)
    echo.
) else (
    echo ❌ ECHEC: Probleme de connexion reseau
    echo.
    echo Solutions possibles:
    echo   1. Verifiez le cable RJ45
    echo   2. Verifiez que l'IC-R8600 est allume
    echo   3. Configurez l'IP de l'IC-R8600 sur *************
    echo   4. Activez 'Remote Control' sur l'IC-R8600
    echo   5. Activez 'CI-V over LAN' sur l'IC-R8600
    echo.
    echo Pour plus d'aide, consultez TECHNICAL.md
    echo.
)

echo Appuyez sur une touche pour fermer...
pause > nul
