import React, { useRef, useEffect, useState } from 'react';
import { BarChart3, Activity, Zap } from 'lucide-react';

/**
 * 📊 ANALYSEUR DE SPECTRE TEMPS RÉEL
 * Affichage spectrogramme et waterfall pour guerre électronique
 */
const SpectrumAnalyzer = ({ spectrumData, signalLevel, signalDetected, width = 400, height = 200 }) => {
  const canvasRef = useRef(null);
  const waterfallRef = useRef(null);
  const [waterfallHistory, setWaterfallHistory] = useState([]);
  const [peakHold, setPeakHold] = useState([]);
  const [showPeakHold, setShowPeakHold] = useState(true);
  const [showWaterfall, setShowWaterfall] = useState(true);

  // Configuration
  const config = {
    backgroundColor: '#000000',
    gridColor: '#003300',
    spectrumColor: '#00ff00',
    peakColor: '#ffff00',
    signalColor: '#ff0000',
    textColor: '#00ff00',
    waterfallHeight: 100,
    maxHistory: 50
  };

  // ============================================================================
  // RENDU SPECTRE PRINCIPAL
  // ============================================================================

  const drawSpectrum = (canvas, spectrum) => {
    const ctx = canvas.getContext('2d');
    const { width, height } = canvas;
    
    // Effacer
    ctx.fillStyle = config.backgroundColor;
    ctx.fillRect(0, 0, width, height);
    
    if (!spectrum || !spectrum.magnitude || spectrum.magnitude.length === 0) {
      // Afficher "NO DATA"
      ctx.fillStyle = config.textColor;
      ctx.font = '16px Courier New';
      ctx.textAlign = 'center';
      ctx.fillText('NO SPECTRUM DATA', width / 2, height / 2);
      return;
    }

    const frequencies = spectrum.frequencies || [];
    const magnitude = spectrum.magnitude || [];
    const binCount = magnitude.length;
    
    if (binCount === 0) return;

    // Grille
    drawGrid(ctx, width, height);
    
    // Échelles
    drawScales(ctx, width, height, frequencies);
    
    // Spectre principal
    drawSpectrumLine(ctx, width, height, magnitude, config.spectrumColor);
    
    // Peak hold
    if (showPeakHold && peakHold.length === magnitude.length) {
      drawSpectrumLine(ctx, width, height, peakHold, config.peakColor);
    }
    
    // Indicateur signal détecté
    if (signalDetected) {
      drawSignalIndicator(ctx, width, height);
    }
    
    // Mise à jour peak hold
    updatePeakHold(magnitude);
  };

  const drawGrid = (ctx, width, height) => {
    ctx.strokeStyle = config.gridColor;
    ctx.lineWidth = 1;
    
    // Lignes horizontales (dB)
    for (let i = 0; i <= 10; i++) {
      const y = (i / 10) * height;
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }
    
    // Lignes verticales (fréquence)
    for (let i = 0; i <= 10; i++) {
      const x = (i / 10) * width;
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
    }
  };

  const drawScales = (ctx, width, height, frequencies) => {
    ctx.fillStyle = config.textColor;
    ctx.font = '10px Courier New';
    ctx.textAlign = 'left';
    
    // Échelle dB (verticale)
    for (let i = 0; i <= 5; i++) {
      const y = (i / 5) * height;
      const dbValue = -20 - (i * 20); // -20 à -120 dB
      ctx.fillText(`${dbValue}dB`, 2, y + 12);
    }
    
    // Échelle fréquence (horizontale)
    if (frequencies.length > 0) {
      const maxFreq = Math.max(...frequencies);
      for (let i = 0; i <= 4; i++) {
        const x = (i / 4) * width;
        const freqValue = (i / 4) * maxFreq;
        const freqLabel = freqValue > 1000 ? `${(freqValue/1000).toFixed(1)}k` : `${freqValue.toFixed(0)}`;
        ctx.fillText(freqLabel, x + 2, height - 2);
      }
    }
  };

  const drawSpectrumLine = (ctx, width, height, magnitude, color) => {
    if (magnitude.length === 0) return;
    
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    const binWidth = width / magnitude.length;
    
    for (let i = 0; i < magnitude.length; i++) {
      const x = i * binWidth;
      // Normaliser magnitude (-120dB à -20dB)
      const normalizedMag = Math.max(0, Math.min(1, (magnitude[i] + 120) / 100));
      const y = height - (normalizedMag * height);
      
      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }
    
    ctx.stroke();
  };

  const drawSignalIndicator = (ctx, width, height) => {
    // Clignotant rouge pour signal détecté
    const time = Date.now();
    const alpha = (Math.sin(time / 200) + 1) / 2; // Oscillation 0-1
    
    ctx.fillStyle = `rgba(255, 0, 0, ${alpha * 0.3})`;
    ctx.fillRect(0, 0, width, height);
    
    // Texte "SIGNAL"
    ctx.fillStyle = config.signalColor;
    ctx.font = 'bold 14px Courier New';
    ctx.textAlign = 'center';
    ctx.fillText('SIGNAL DETECTED', width / 2, 20);
  };

  const updatePeakHold = (magnitude) => {
    if (!showPeakHold) return;
    
    setPeakHold(prev => {
      if (prev.length !== magnitude.length) {
        return [...magnitude];
      }
      
      return prev.map((peak, i) => Math.max(peak * 0.99, magnitude[i])); // Décroissance lente
    });
  };

  // ============================================================================
  // RENDU WATERFALL
  // ============================================================================

  const drawWaterfall = (canvas, history) => {
    if (!showWaterfall || history.length === 0) return;
    
    const ctx = canvas.getContext('2d');
    const { width, height } = canvas;
    
    // Effacer
    ctx.fillStyle = config.backgroundColor;
    ctx.fillRect(0, 0, width, height);
    
    const lineHeight = height / config.maxHistory;
    
    history.forEach((spectrum, historyIndex) => {
      if (!spectrum || !spectrum.magnitude) return;
      
      const y = historyIndex * lineHeight;
      const binWidth = width / spectrum.magnitude.length;
      
      spectrum.magnitude.forEach((mag, binIndex) => {
        const x = binIndex * binWidth;
        
        // Convertir magnitude en couleur
        const intensity = Math.max(0, Math.min(1, (mag + 120) / 100));
        const color = intensityToColor(intensity);
        
        ctx.fillStyle = color;
        ctx.fillRect(x, y, binWidth, lineHeight);
      });
    });
    
    // Ligne de temps actuelle
    ctx.strokeStyle = config.spectrumColor;
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(0, (history.length - 1) * lineHeight);
    ctx.lineTo(width, (history.length - 1) * lineHeight);
    ctx.stroke();
  };

  const intensityToColor = (intensity) => {
    // Palette de couleurs pour waterfall (bleu -> vert -> jaune -> rouge)
    if (intensity < 0.25) {
      const t = intensity * 4;
      return `rgb(0, 0, ${Math.floor(64 + t * 191)})`;
    } else if (intensity < 0.5) {
      const t = (intensity - 0.25) * 4;
      return `rgb(0, ${Math.floor(t * 255)}, ${Math.floor(255 - t * 255)})`;
    } else if (intensity < 0.75) {
      const t = (intensity - 0.5) * 4;
      return `rgb(${Math.floor(t * 255)}, 255, 0)`;
    } else {
      const t = (intensity - 0.75) * 4;
      return `rgb(255, ${Math.floor(255 - t * 255)}, 0)`;
    }
  };

  // ============================================================================
  // EFFECTS
  // ============================================================================

  useEffect(() => {
    if (spectrumData) {
      // Mettre à jour historique waterfall
      setWaterfallHistory(prev => {
        const newHistory = [...prev, spectrumData];
        return newHistory.slice(-config.maxHistory);
      });
    }
  }, [spectrumData]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (canvas && spectrumData) {
      drawSpectrum(canvas, spectrumData);
    }
  }, [spectrumData, signalDetected, showPeakHold]);

  useEffect(() => {
    const canvas = waterfallRef.current;
    if (canvas && showWaterfall) {
      drawWaterfall(canvas, waterfallHistory);
    }
  }, [waterfallHistory, showWaterfall]);

  // Animation continue pour signal détecté
  useEffect(() => {
    if (signalDetected) {
      const interval = setInterval(() => {
        const canvas = canvasRef.current;
        if (canvas && spectrumData) {
          drawSpectrum(canvas, spectrumData);
        }
      }, 100);
      
      return () => clearInterval(interval);
    }
  }, [signalDetected, spectrumData]);

  // ============================================================================
  // RENDER
  // ============================================================================

  const styles = {
    container: {
      backgroundColor: '#000',
      border: '2px solid #00ff00',
      borderRadius: '5px',
      padding: '10px',
      fontFamily: 'Courier New, monospace'
    },
    header: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: '10px',
      color: '#00ff00'
    },
    title: {
      display: 'flex',
      alignItems: 'center',
      gap: '5px',
      fontSize: '14px',
      fontWeight: 'bold'
    },
    controls: {
      display: 'flex',
      gap: '10px',
      fontSize: '12px'
    },
    button: {
      padding: '2px 8px',
      backgroundColor: '#001100',
      border: '1px solid #00ff00',
      color: '#00ff00',
      cursor: 'pointer',
      fontSize: '10px'
    },
    canvas: {
      border: '1px solid #003300',
      display: 'block',
      marginBottom: '5px'
    },
    info: {
      display: 'flex',
      justifyContent: 'space-between',
      fontSize: '10px',
      color: '#00ff00'
    }
  };

  return (
    <div style={styles.container}>
      {/* En-tête */}
      <div style={styles.header}>
        <div style={styles.title}>
          <BarChart3 size={16} />
          ANALYSEUR SPECTRE
        </div>
        
        <div style={styles.controls}>
          <button
            style={{
              ...styles.button,
              backgroundColor: showPeakHold ? '#001100' : '#000'
            }}
            onClick={() => setShowPeakHold(!showPeakHold)}
          >
            PEAK
          </button>
          
          <button
            style={{
              ...styles.button,
              backgroundColor: showWaterfall ? '#001100' : '#000'
            }}
            onClick={() => setShowWaterfall(!showWaterfall)}
          >
            WATERFALL
          </button>
          
          <button
            style={styles.button}
            onClick={() => setPeakHold([])}
          >
            CLEAR
          </button>
        </div>
      </div>

      {/* Canvas spectre principal */}
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        style={styles.canvas}
      />

      {/* Canvas waterfall */}
      {showWaterfall && (
        <canvas
          ref={waterfallRef}
          width={width}
          height={config.waterfallHeight}
          style={styles.canvas}
        />
      )}

      {/* Informations */}
      <div style={styles.info}>
        <span>Signal: {signalLevel.toFixed(1)} dBFS</span>
        <span>
          {signalDetected && (
            <>
              <Zap size={12} style={{ color: '#ff0000' }} />
              DÉTECTÉ
            </>
          )}
        </span>
        <span>Bins: {spectrumData?.magnitude?.length || 0}</span>
      </div>
    </div>
  );
};

export default SpectrumAnalyzer;
