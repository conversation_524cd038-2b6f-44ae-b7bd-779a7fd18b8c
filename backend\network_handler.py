"""
Handler réseau pour communication directe avec IC-R8600 via RJ45
Implémente le protocole réseau ICOM (reverse-engineered)
"""

import socket
import struct
import time
import logging
from typing import Optional, Dict, Any, List
from dataclasses import dataclass

@dataclass
class NetworkConfig:
    """Configuration réseau optimisée pour IC-R8600"""
    host: str = "*************"
    port: int = 50001
    timeout: float = 3.0  # Timeout réduit pour réactivité
    retry_count: int = 3  # Nombre de tentatives
    retry_delay: float = 0.5  # <PERSON><PERSON>lai entre tentatives
    keepalive: bool = True  # Maintenir la connexion
    buffer_size: int = 8192  # Taille buffer optimisée
    
class ICOMNetworkHandler:
    """Handler pour communication réseau directe avec IC-R8600"""
    
    def __init__(self, config: NetworkConfig = None):
        self.config = config or NetworkConfig()
        self.socket = None
        self.connected = False
        
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def connect(self) -> bool:
        """Établit la connexion réseau robuste avec l'IC-R8600"""
        for attempt in range(self.config.retry_count):
            try:
                self.logger.info(f"🔌 Tentative {attempt + 1}/{self.config.retry_count} vers {self.config.host}:{self.config.port}")

                # Créer socket avec options optimisées
                self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                self.socket.settimeout(self.config.timeout)

                # Options socket pour performance
                if self.config.keepalive:
                    self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
                self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, self.config.buffer_size)
                self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, self.config.buffer_size)

                # Tentative de connexion
                self.socket.connect((self.config.host, self.config.port))

                # Test de communication
                if self._test_communication():
                    self.connected = True
                    self.logger.info(f"✅ Connexion réseau établie (tentative {attempt + 1})")
                    return True
                else:
                    self.logger.warning(f"⚠️ Connexion établie mais communication échouée")
                    self._close_socket()

            except socket.timeout:
                self.logger.warning(f"⏱️ Timeout connexion (tentative {attempt + 1})")
                self._close_socket()
            except ConnectionRefusedError:
                self.logger.warning(f"🚫 Connexion refusée (tentative {attempt + 1})")
                self._close_socket()
            except Exception as e:
                self.logger.error(f"❌ Erreur connexion (tentative {attempt + 1}): {e}")
                self._close_socket()

            # Attendre avant la prochaine tentative
            if attempt < self.config.retry_count - 1:
                import time
                time.sleep(self.config.retry_delay)

        self.logger.error("❌ Toutes les tentatives de connexion ont échoué")
        return False

    def _close_socket(self):
        """Ferme proprement le socket"""
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
    
    def disconnect(self):
        """Ferme la connexion réseau"""
        if self.socket:
            try:
                self.socket.close()
                self.connected = False
                self.logger.info("Connexion fermée")
            except:
                pass
    
    def _test_communication(self) -> bool:
        """Test la communication avec l'IC-R8600"""
        try:
            # Commande CI-V simple : lecture fréquence
            test_command = bytes([0xFE, 0xFE, 0x96, 0xDF, 0x03, 0xFD])

            self.logger.info("📡 Test communication CI-V...")
            self.socket.send(test_command)

            # Attendre la réponse avec timeout court
            self.socket.settimeout(1.0)
            response = self.socket.recv(self.config.buffer_size)

            if response and len(response) > 0:
                self.logger.info(f"✅ Communication CI-V OK: {response.hex()}")
                return True
            else:
                self.logger.warning("⚠️ Pas de réponse CI-V")
                return False

        except socket.timeout:
            self.logger.warning("⏱️ Timeout test communication")
            return False
        except Exception as e:
            self.logger.error(f"❌ Erreur test communication: {e}")
            return False
        finally:
            # Restaurer le timeout original
            if self.socket:
                self.socket.settimeout(self.config.timeout)
    
    def _build_init_packet(self) -> bytes:
        """Construit le paquet d'initialisation"""
        # Paquet d'initialisation basé sur l'analyse du protocole RS-R8600
        # Ces valeurs sont des exemples et doivent être adaptées
        header = b'\x00\x00\x00\x10'  # Longueur du paquet
        command = b'\x01\x00'         # Commande d'initialisation
        data = b'\x00' * 10           # Données d'initialisation
        
        return header + command + data
    
    def _build_command_packet(self, command_type: int, data: bytes = b'') -> bytes:
        """Construit un paquet de commande réseau"""
        # Structure du paquet ICOM (hypothétique)
        packet_length = 8 + len(data)
        header = struct.pack('>I', packet_length)  # Longueur en big-endian
        cmd_header = struct.pack('>HH', command_type, len(data))
        
        return header + cmd_header + data
    
    def _send_command(self, packet: bytes) -> Optional[bytes]:
        """Envoie une commande et lit la réponse"""
        if not self.connected or not self.socket:
            return None
        
        try:
            self.socket.send(packet)
            response = self.socket.recv(1024)
            return response
            
        except Exception as e:
            self.logger.error(f"Erreur envoi commande: {e}")
            return None
    
    def set_frequency(self, freq_hz: int) -> bool:
        """Définit la fréquence via réseau"""
        try:
            # Conversion fréquence en format réseau ICOM
            freq_data = struct.pack('>Q', freq_hz)  # 8 bytes, big-endian
            packet = self._build_command_packet(0x0001, freq_data)
            
            response = self._send_command(packet)
            
            if response:
                self.logger.info(f"Fréquence {freq_hz} Hz définie")
                return True
                
        except Exception as e:
            self.logger.error(f"Erreur set_frequency: {e}")
        
        return False
    
    def set_mode(self, mode: str) -> bool:
        """Définit le mode de modulation"""
        mode_map = {
            "LSB": 0x00, "USB": 0x01, "AM": 0x02, "CW": 0x03,
            "FM": 0x05, "WFM": 0x06, "CWR": 0x07, "RTTY": 0x08
        }
        
        if mode not in mode_map:
            return False
        
        try:
            mode_data = struct.pack('B', mode_map[mode])
            packet = self._build_command_packet(0x0002, mode_data)
            
            response = self._send_command(packet)
            
            if response:
                self.logger.info(f"Mode {mode} défini")
                return True
                
        except Exception as e:
            self.logger.error(f"Erreur set_mode: {e}")
        
        return False
    
    def power_control(self, power_on: bool) -> bool:
        """Contrôle l'alimentation"""
        try:
            power_data = struct.pack('B', 1 if power_on else 0)
            packet = self._build_command_packet(0x0003, power_data)
            
            response = self._send_command(packet)
            
            if response:
                state = "ON" if power_on else "OFF"
                self.logger.info(f"Alimentation {state}")
                return True
                
        except Exception as e:
            self.logger.error(f"Erreur power_control: {e}")
        
        return False
    
    def get_status(self) -> Dict[str, Any]:
        """Récupère l'état du récepteur"""
        try:
            packet = self._build_command_packet(0x0010)  # Commande status
            response = self._send_command(packet)
            
            if response and len(response) >= 16:
                # Décodage de la réponse (à adapter selon le protocole)
                freq = struct.unpack('>Q', response[8:16])[0]
                mode_byte = response[16] if len(response) > 16 else 0
                
                return {
                    "frequency": freq,
                    "mode": self._decode_mode(mode_byte),
                    "power_on": True,
                    "connected": True
                }
                
        except Exception as e:
            self.logger.error(f"Erreur get_status: {e}")
        
        return {
            "frequency": 0,
            "mode": "FM",
            "power_on": False,
            "connected": self.connected
        }
    
    def _decode_mode(self, mode_byte: int) -> str:
        """Décode le mode depuis la réponse"""
        mode_map = {
            0x00: "LSB", 0x01: "USB", 0x02: "AM", 0x03: "CW",
            0x05: "FM", 0x06: "WFM", 0x07: "CWR", 0x08: "RTTY"
        }
        return mode_map.get(mode_byte, "FM")

class ICOMNetworkBridge:
    """Bridge entre l'API REST et le handler réseau"""
    
    def __init__(self, network_config: NetworkConfig = None):
        self.network_handler = ICOMNetworkHandler(network_config)
        self.logger = logging.getLogger(__name__)
    
    async def initialize(self) -> bool:
        """Initialise la connexion réseau"""
        return self.network_handler.connect()
    
    async def cleanup(self):
        """Nettoie les ressources"""
        self.network_handler.disconnect()
    
    async def execute_command(self, command: str, **kwargs) -> Dict[str, Any]:
        """Exécute une commande sur le récepteur"""
        try:
            if command == "set_frequency":
                success = self.network_handler.set_frequency(kwargs.get("frequency", 0))
                return {"success": success, "message": f"Fréquence définie"}
            
            elif command == "set_mode":
                success = self.network_handler.set_mode(kwargs.get("mode", "FM"))
                return {"success": success, "message": f"Mode défini"}
            
            elif command == "power_on":
                success = self.network_handler.power_control(True)
                return {"success": success, "message": "Récepteur allumé"}
            
            elif command == "power_off":
                success = self.network_handler.power_control(False)
                return {"success": success, "message": "Récepteur éteint"}
            
            elif command == "get_status":
                status = self.network_handler.get_status()
                return {"success": True, "data": status}
            
            else:
                return {"success": False, "message": "Commande inconnue"}
                
        except Exception as e:
            self.logger.error(f"Erreur execute_command: {e}")
            return {"success": False, "message": str(e)}
