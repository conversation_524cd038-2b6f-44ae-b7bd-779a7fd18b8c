"""
🔧 TEST DIRECT IC-R8600
Test de communication directe avec l'IC-R8600 via série
"""

import serial
import time
import sys

def test_icom_communication():
    """Test direct de communication avec l'IC-R8600"""
    
    print("🔧 TEST DIRECT IC-R8600")
    print("=" * 50)
    
    # Configuration série
    port = "COM6"
    baudrate = 19200
    
    try:
        # Ouvrir la connexion série
        print(f"📡 Connexion à {port} à {baudrate} bauds...")
        ser = serial.Serial(
            port=port,
            baudrate=baudrate,
            bytesize=serial.EIGHTBITS,
            parity=serial.PARITY_NONE,
            stopbits=serial.STOPBITS_ONE,
            timeout=5.0,  # Timeout plus long
            write_timeout=5.0
        )
        
        print(f"✅ Port série ouvert: {ser.is_open}")
        
        # Attendre un peu pour la stabilisation
        time.sleep(1)
        
        # Commandes de test CI-V
        test_commands = [
            {
                "name": "Lecture fréquence",
                "command": bytes([0xFE, 0xFE, 0x96, 0xDF, 0x03, 0xFD]),
                "expected_length": 11
            },
            {
                "name": "Lecture mode",
                "command": bytes([0xFE, 0xFE, 0x96, 0xDF, 0x04, 0xFD]),
                "expected_length": 7
            },
            {
                "name": "Test écho",
                "command": bytes([0xFE, 0xFE, 0x96, 0xDF, 0xFD]),
                "expected_length": 6
            }
        ]
        
        success_count = 0
        
        for i, test in enumerate(test_commands, 1):
            print(f"\n🧪 Test {i}/3: {test['name']}")
            print(f"   Commande: {test['command'].hex().upper()}")
            
            try:
                # Vider le buffer de réception
                ser.reset_input_buffer()
                
                # Envoyer la commande
                bytes_written = ser.write(test['command'])
                print(f"   Envoyé: {bytes_written} bytes")
                
                # Attendre la réponse
                time.sleep(0.5)  # Attendre un peu plus
                
                # Lire la réponse
                response = ser.read(50)  # Lire jusqu'à 50 bytes
                
                if response:
                    print(f"   ✅ Réponse reçue: {response.hex().upper()} ({len(response)} bytes)")
                    
                    # Vérifier si c'est une réponse CI-V valide
                    if len(response) >= 6 and response[0:2] == bytes([0xFE, 0xFE]):
                        print(f"   ✅ Format CI-V valide")
                        success_count += 1
                        
                        # Analyser la réponse
                        if test['name'] == "Lecture fréquence" and len(response) >= 11:
                            freq_bytes = response[6:11]
                            print(f"   📊 Données fréquence: {freq_bytes.hex().upper()}")
                        elif test['name'] == "Lecture mode" and len(response) >= 7:
                            mode_byte = response[6]
                            print(f"   📊 Code mode: 0x{mode_byte:02X}")
                    else:
                        print(f"   ⚠️ Format CI-V invalide")
                else:
                    print(f"   ❌ Aucune réponse reçue")
                    
            except Exception as e:
                print(f"   ❌ Erreur: {e}")
        
        # Résumé
        print(f"\n📊 RÉSULTATS:")
        print(f"   Tests réussis: {success_count}/3")
        
        if success_count > 0:
            print(f"   ✅ IC-R8600 répond correctement!")
            print(f"   🎯 La communication CI-V fonctionne")
        else:
            print(f"   ❌ Aucune réponse valide de l'IC-R8600")
            print(f"   🔍 Vérifiez:")
            print(f"      - IC-R8600 allumé")
            print(f"      - CI-V activé dans les menus")
            print(f"      - Adresse CI-V = 96h (150)")
            print(f"      - Vitesse CI-V = 19200 bauds")
        
        ser.close()
        
    except serial.SerialException as e:
        print(f"❌ Erreur port série: {e}")
        print(f"🔍 Vérifiez que COM6 est disponible")
    except Exception as e:
        print(f"❌ Erreur générale: {e}")

if __name__ == "__main__":
    test_icom_communication()
    
    print(f"\n⏸️ Appuyez sur Entrée pour continuer...")
    input()
