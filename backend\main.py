import logging
import asyncio
from contextlib import asynccontextmanager
from fastapi import <PERSON><PERSON><PERSON>, WebSocket, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from typing import Dict, Any, List, Optional
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
import time
from icom_handler import ICOMHandler
from audio_recorder import AudioRecorder

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Cache Manager
class CacheManager:
    def __init__(self):
        self.cache = {}
        self.last_update = {}
        self.cache_duration = 2.0
    
    def get(self, key: str) -> Optional[Any]:
        if key in self.cache and time.time() - self.last_update[key] < self.cache_duration:
            return self.cache[key]
        return None
    
    def set(self, key: str, value: Any):
        self.cache[key] = value
        self.last_update[key] = time.time()
    
    def invalidate(self, key: str):
        if key in self.cache:
            del self.cache[key]
        if key in self.last_update:
            del self.last_update[key]

cache_manager = CacheManager()

# Async Radio Handler
class AsyncRadioHandler:
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.icom = None
        self.audio_recorder = None
        self.last_command_time = 0
        self.command_cooldown = 0.5
    
    async def init_hardware(self):
        loop = asyncio.get_event_loop()
        try:
            await loop.run_in_executor(self.executor, self._init_hardware_sync)
            logger.info("✅ Matériel initialisé avec succès")
        except Exception as e:
            logger.error(f"❌ Erreur initialisation matériel: {e}")
            self.icom = None  # Mode simulation
    
    def _init_hardware_sync(self):
        try:
            self.icom = ICOMHandler(port="COM6", baudrate=19200)
            self.audio_recorder = AudioRecorder()
        except Exception as e:
            logger.error(f"Échec connexion série: {e}")
            raise
    
    async def ensure_connection(self):
        if not self.icom or not self.icom.connected:  # Correction: utiliser 'connected' au lieu de 'is_connected()'
            logger.warning("Connexion perdue, tentative de reconnexion...")
            try:
                await self.init_hardware()
                return True
            except Exception as e:
                logger.error(f"Échec reconnexion: {e}")
                return False
        return True
    
    async def get_status_async(self) -> Dict[str, Any]:
        if not await self.ensure_connection():
            return {
                "connected": False,
                "frequency": 145000000,
                "mode": "FM",
                "rf_gain": 128,
                "rssi": 0,
                "power_on": False,
                "filter_width": 15000,
                "error": "Connexion au récepteur perdue"
            }
        
        cache_key = "radio_status"
        cached_status = cache_manager.get(cache_key)
        if cached_status:
            return cached_status
        
        loop = asyncio.get_event_loop()
        try:
            status = await asyncio.wait_for(
                loop.run_in_executor(self.executor, self._get_status_sync),
                timeout=30.0
            )
            cache_manager.set(cache_key, status)
            return status
        except asyncio.TimeoutError:
            logger.warning("⚠️ Timeout lecture état radio")
            return {"connected": False, "error": "Timeout de communication"}
        except Exception as e:
            logger.error(f"❌ Erreur lecture état: {e}")
            return {"connected": False, "error": str(e)}
    
    def _get_status_sync(self) -> Dict[str, Any]:
        if not self.icom:
            return {"connected": False, "error": "ICOM non initialisé"}
        return self.icom.get_status()
    
    async def send_command_async(self, command: Dict[str, Any]) -> Dict[str, Any]:
        if not await self.ensure_connection():
            return {"success": False, "message": "Connexion au récepteur perdue"}
        
        current_time = time.time()
        if current_time - self.last_command_time < self.command_cooldown:
            await asyncio.sleep(self.command_cooldown)
        self.last_command_time = current_time
        
        cache_manager.invalidate("radio_status")
        loop = asyncio.get_event_loop()
        try:
            result = await asyncio.wait_for(
                loop.run_in_executor(self.executor, self._send_command_sync, command),
                timeout=30.0
            )
            return result
        except asyncio.TimeoutError:
            logger.warning("⚠️ Timeout envoi commande")
            return {"success": False, "message": "Timeout lors de l'envoi de la commande"}
        except Exception as e:
            logger.error(f"❌ Erreur envoi commande: {e}")
            return {"success": False, "message": str(e)}
    
    def _send_command_sync(self, command: Dict[str, Any]) -> Dict[str, Any]:
        if not self.icom:
            return {"success": False, "message": "ICOM non initialisé"}
        
        result = {"success": False, "message": ""}
        try:
            if "frequency" in command:
                freq = int(command["frequency"])
                if self.icom.set_frequency(freq):
                    result["success"] = True
                    result["message"] = f"Fréquence: {freq} Hz"
                else:
                    result["message"] = "Erreur fréquence"
            
            if "mode" in command:
                mode = command["mode"]
                filter_width = command.get("filter", 1)
                if self.icom.set_mode(mode, filter_width):
                    result["success"] = True
                    result["message"] += f" Mode: {mode}"
                else:
                    result["message"] += " Erreur mode"
                    
            if "power_on" in command:
                if command["power_on"]:
                    result["success"] = self.icom.power_on()
                    result["message"] += " Récepteur allumé"
                else:
                    result["success"] = self.icom.power_off()
                    result["message"] += " Récepteur éteint"
                    
        except Exception as e:
            result["message"] = f"Erreur: {str(e)}"
        
        return result
    
    async def get_recordings_async(self) -> Dict[str, Any]:
        if not self.audio_recorder:
            return {"recordings": [], "total": 0, "error": "Enregistreur non initialisé"}
        
        cache_key = "recordings_list"
        cached_recordings = cache_manager.get(cache_key)
        if cached_recordings:
            return cached_recordings
        
        loop = asyncio.get_event_loop()
        try:
            recordings = await asyncio.wait_for(
                loop.run_in_executor(self.executor, self._get_recordings_sync),
                timeout=10.0
            )
            cache_manager.set(cache_key, recordings)
            return recordings
        except asyncio.TimeoutError:
            logger.warning("⚠️ Timeout liste enregistrements")
            return {"recordings": [], "total": 0, "error": "Timeout"}
        except Exception as e:
            logger.error(f"❌ Erreur liste enregistrements: {e}")
            return {"recordings": [], "total": 0, "error": str(e)}
    
    def _get_recordings_sync(self) -> Dict[str, Any]:
        recordings = self.audio_recorder.list_recordings()
        return {
            "recordings": recordings,
            "total": len(recordings),
            "timestamp": datetime.now().isoformat()
        }

radio_handler = AsyncRadioHandler()

@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("🚀 Démarrage du backend IC-R8600")
    try:
        await radio_handler.init_hardware()
    except Exception as e:
        logger.error(f"❌ Échec initialisation: {e}")
    
    yield
    
    logger.info("🛑 Arrêt du backend")
    radio_handler.executor.shutdown(wait=True)

app = FastAPI(title="IC-R8600 Control", lifespan=lifespan)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "IC-R8600 Control API", "status": "running"}

@app.get("/api/status")
async def get_status():
    return await radio_handler.get_status_async()

@app.post("/api/command")
async def send_command(command: Dict[str, Any]):
    return await radio_handler.send_command_async(command)

@app.get("/api/recordings")
async def list_recordings():
    return await radio_handler.get_recordings_async()

class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.last_broadcast = 0
        self.broadcast_interval = 3.0
    
    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
    
    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
    
    async def broadcast_status(self):
        current_time = time.time()
        if current_time - self.last_broadcast < self.broadcast_interval:
            return
        
        self.last_broadcast = current_time
        status = await radio_handler.get_status_async()
        status["type"] = "status_update"
        
        disconnected = []
        for connection in self.active_connections:
            try:
                await asyncio.wait_for(connection.send_json(status), timeout=5.0)
            except:
                disconnected.append(connection)
        
        for conn in disconnected:
            self.disconnect(conn)

manager = ConnectionManager()

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    logger.info("✅ WebSocket connecté")
    
    try:
        while True:
            try:
                data = await asyncio.wait_for(websocket.receive_text(), timeout=10.0)
                logger.debug(f"Message reçu: {data}")
                if data == "ping":
                    await websocket.send_json({
                        "type": "pong",
                        "timestamp": datetime.now().isoformat()
                    })
            except asyncio.TimeoutError:
                await manager.broadcast_status()
            except Exception as e:
                logger.error(f"Erreur WebSocket: {e}")
                break
    except Exception as e:
        logger.error(f"❌ Erreur WebSocket: {e}")
    finally:
        manager.disconnect(websocket)
        logger.info("🔌 WebSocket déconnecté")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")