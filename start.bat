@echo off
echo ========================================
echo    ICOM IC-R8600 Controller - MODE RESEAU
echo    Demarrage automatique (RJ45 uniquement)
echo ========================================
echo.

echo [1/5] Test de connexion reseau IC-R8600...
cd backend
python test_network_auto.py
if %errorlevel% neq 0 (
    echo.
    echo ❌ ERREUR: Connexion reseau echouee
    echo    Verifiez:
    echo    - Cable RJ45 connecte
    echo    - IC-R8600 allume
    echo    - Configuration IP: *************
    echo    - Remote Control active sur IC-R8600
    echo.
    pause
    exit /b 1
)

echo.
echo [2/5] Installation des dependances Python...
pip install -r ../requirements.txt
if %errorlevel% neq 0 (
    echo Erreur installation Python
    pause
    exit /b 1
)

echo.
echo [3/5] Installation des dependances Node.js...
cd ../frontend
call npm install
if %errorlevel% neq 0 (
    echo Erreur installation Node.js
    pause
    exit /b 1
)

echo.
echo [4/5] Demarrage du backend FastAPI (<PERSON>seau)...
cd ../backend
start "Backend FastAPI - Reseau" cmd /k "python main.py"

echo.
echo [5/5] Demarrage du frontend React...
cd ../frontend
timeout /t 3 /nobreak > nul
start "Frontend React" cmd /k "npm run dev"

echo.
echo ========================================
echo    Services demarres en MODE RESEAU !
echo    Backend:  http://localhost:8000
echo    Frontend: http://localhost:5173
echo    API Docs: http://localhost:8000/docs
echo.
echo    🌐 Connexion: RJ45 uniquement
echo    📡 IP IC-R8600: *************:50001
echo    ❌ USB desactive (pas de fallback)
echo ========================================
echo.
echo Appuyez sur une touche pour fermer...
pause > nul
