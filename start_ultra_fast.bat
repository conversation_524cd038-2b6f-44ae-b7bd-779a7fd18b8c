@echo off
echo.
echo ========================================
echo 🚀 ICOM IC-R8600 ULTRA-FAST CONTROLLER
echo    MODE RESEAU UNIQUEMENT - RJ45
echo ========================================
echo.

echo [1/3] Test connexion reseau ultra-rapide...
cd backend
python test_network_auto.py
if %errorlevel% neq 0 (
    echo.
    echo ❌ ERREUR: Connexion reseau echouee
    echo    Mode ultra-rapide necessite une connexion reseau stable
    echo    📡 IP requise: *************:50001
    echo    🔌 Cable RJ45 requis
    echo.
    pause
    exit /b 1
)

echo.
echo [2/3] Configuration performance maximale...
REM Configuration pour performance maximale
set PYTHONUNBUFFERED=1
set UVLOOP_ENABLED=1

echo ✅ Test reseau reussi - Demarrage serveur ultra-rapide...
echo 🌐 Mode: <PERSON><PERSON> uniquement (pas de fallback USB)
echo 📡 IP IC-R8600: *************:50001
echo.

REM Démarrer le serveur backend ultra-rapide
start "ICOM Backend Ultra-Fast - Reseau" cmd /k "python main_ultra_fast.py"

echo.
echo [3/3] Demarrage interface web...
REM Attendre que le serveur démarre
timeout /t 3 /nobreak > nul

REM Démarrer le frontend
cd ..\frontend
start "ICOM Frontend" cmd /k "npm run dev"

echo.
echo ✅ Système ultra-rapide démarré en MODE RESEAU !
echo.
echo 📊 Interfaces disponibles:
echo    - Backend API: http://localhost:8000
echo    - Frontend Web: http://localhost:5173
echo    - WebSocket: ws://localhost:8000/ws
echo.
echo 🎯 Fonctionnalités ultra-rapides (RJ45):
echo    - Latence < 5ms pour les commandes
echo    - Mise à jour statut 50Hz (20ms)
echo    - Audio temps réel via réseau
echo    - Contrôles clavier instantanés
echo    - Pas de dépendance USB
echo.
echo 🔧 Raccourcis clavier:
echo    - Flèches: Changement fréquence
echo    - Espace: Power ON/OFF
echo    - L: Verrouiller/Déverrouiller
echo    - M: Mute audio
echo    - R: Enregistrement
echo.
echo Appuyez sur une touche pour ouvrir l'interface...
pause > nul

REM Ouvrir l'interface web
start http://localhost:5173

echo.
echo 🚀 Interface ouverte ! Contrôle ultra-rapide activé.
echo.
pause
