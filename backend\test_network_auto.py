#!/usr/bin/env python3
"""
🔍 Test automatique de connexion réseau IC-R8600
Script de validation avant démarrage du serveur
"""

import socket
import subprocess
import sys
import time
import json
from typing import Dict, List, Optional, Tuple

class NetworkTester:
    """Testeur automatique de connexion réseau IC-R8600"""
    
    def __init__(self):
        self.config = self._load_config()
        self.results = {}
        
    def _load_config(self) -> Dict:
        """Charge la configuration depuis config.json"""
        try:
            with open("config.json", "r") as f:
                return json.load(f)
        except:
            return {
                "icom": {
                    "udp_host": "*************",
                    "udp_port": 50001
                }
            }
    
    def run_full_test(self) -> bool:
        """Exécute tous les tests de connectivité"""
        print("🔍 TEST AUTOMATIQUE CONNEXION RÉSEAU IC-R8600")
        print("=" * 50)
        
        tests = [
            ("ping", self.test_ping),
            ("port_scan", self.test_port_connectivity),
            ("udp_communication", self.test_udp_communication),
            ("tcp_connection", self.test_tcp_connection),
            ("network_discovery", self.discover_ic_r8600)
        ]
        
        all_passed = True
        
        for test_name, test_func in tests:
            print(f"\n🧪 Test: {test_name}")
            try:
                result = test_func()
                self.results[test_name] = result
                status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
                print(f"   {status}")
            except Exception as e:
                self.results[test_name] = False
                print(f"   ❌ ERREUR: {e}")
                all_passed = False
        
        self._print_summary()
        return all_passed
    
    def test_ping(self) -> bool:
        """Test de ping réseau"""
        host = self.config["icom"]["udp_host"]
        try:
            result = subprocess.run(
                ["ping", "-n", "3", host],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                # Extraire le temps de réponse moyen
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'Moyenne' in line or 'Average' in line:
                        print(f"   📊 {line.strip()}")
                return True
            else:
                print(f"   ⚠️ Ping échoué vers {host}")
                return False
                
        except Exception as e:
            print(f"   ❌ Erreur ping: {e}")
            return False
    
    def test_port_connectivity(self) -> bool:
        """Test de connectivité des ports"""
        host = self.config["icom"]["udp_host"]
        ports_to_test = [50001, 23, 80, 443]  # CI-V, Telnet, HTTP, HTTPS
        
        open_ports = []
        
        for port in ports_to_test:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(2.0)
                result = sock.connect_ex((host, port))
                sock.close()
                
                if result == 0:
                    open_ports.append(port)
                    print(f"   ✅ Port {port} ouvert")
                else:
                    print(f"   ❌ Port {port} fermé")
                    
            except Exception as e:
                print(f"   ⚠️ Erreur test port {port}: {e}")
        
        # Au minimum le port CI-V doit être ouvert
        return 50001 in open_ports
    
    def test_udp_communication(self) -> bool:
        """Test de communication UDP CI-V"""
        host = self.config["icom"]["udp_host"]
        port = self.config["icom"]["udp_port"]
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(3.0)
            
            # Commande CI-V : lecture fréquence
            command = bytes([0xFE, 0xFE, 0x96, 0xDF, 0x03, 0xFD])
            print(f"   📡 Envoi commande CI-V: {command.hex()}")
            
            sock.sendto(command, (host, port))
            
            try:
                response, addr = sock.recvfrom(1024)
                print(f"   ✅ Réponse reçue: {response.hex()}")
                print(f"   📍 Source: {addr}")
                return True
            except socket.timeout:
                print("   ⏱️ Timeout - Pas de réponse CI-V")
                return False
                
        except Exception as e:
            print(f"   ❌ Erreur UDP: {e}")
            return False
        finally:
            if 'sock' in locals():
                sock.close()
    
    def test_tcp_connection(self) -> bool:
        """Test de connexion TCP"""
        host = self.config["icom"]["udp_host"]
        
        # Tester différents ports TCP
        tcp_ports = [23, 80, 443, 8080]
        
        for port in tcp_ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(2.0)
                
                result = sock.connect_ex((host, port))
                if result == 0:
                    print(f"   ✅ TCP {port} accessible")
                    sock.close()
                    return True
                sock.close()
                
            except Exception:
                continue
        
        print("   ❌ Aucun port TCP accessible")
        return False
    
    def discover_ic_r8600(self) -> bool:
        """Découverte automatique de l'IC-R8600 sur le réseau"""
        print("   🔍 Scan réseau pour IC-R8600...")
        
        # Scanner la plage 192.168.1.x
        base_ip = "192.168.1."
        found_devices = []
        
        # Test rapide sur quelques IPs communes
        common_ips = [100, 101, 102, 150, 200, 201, 202]
        
        for ip_suffix in common_ips:
            ip = f"{base_ip}{ip_suffix}"
            if self._test_ic_r8600_at_ip(ip):
                found_devices.append(ip)
                print(f"   🎯 IC-R8600 trouvé à {ip}")
        
        if found_devices:
            print(f"   ✅ {len(found_devices)} dispositif(s) trouvé(s)")
            # Mettre à jour la config avec la première IP trouvée
            if found_devices[0] != self.config["icom"]["udp_host"]:
                print(f"   🔄 Suggestion: Utiliser IP {found_devices[0]}")
            return True
        else:
            print("   ❌ Aucun IC-R8600 détecté")
            return False
    
    def _test_ic_r8600_at_ip(self, ip: str) -> bool:
        """Test si un IC-R8600 répond à une IP donnée"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(1.0)
            
            # Commande CI-V spécifique IC-R8600
            command = bytes([0xFE, 0xFE, 0x96, 0xDF, 0x03, 0xFD])
            sock.sendto(command, (ip, 50001))
            
            response, _ = sock.recvfrom(1024)
            sock.close()
            
            # Vérifier que c'est bien une réponse CI-V valide
            return len(response) > 6 and response[0:2] == bytes([0xFE, 0xFE])
            
        except:
            return False
    
    def _print_summary(self):
        """Affiche le résumé des tests"""
        print("\n" + "=" * 50)
        print("📋 RÉSUMÉ DES TESTS")
        print("=" * 50)
        
        passed = sum(1 for result in self.results.values() if result)
        total = len(self.results)
        
        for test_name, result in self.results.items():
            status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
            print(f"{test_name:20} : {status}")
        
        print(f"\n📊 Score: {passed}/{total} tests réussis")
        
        if passed == total:
            print("🎉 Tous les tests sont réussis - Connexion réseau OK!")
        elif passed >= total // 2:
            print("⚠️ Connexion partielle - Vérifiez la configuration")
        else:
            print("❌ Problème de connexion - Vérifiez le réseau et l'IC-R8600")
        
        self._print_recommendations()
    
    def _print_recommendations(self):
        """Affiche les recommandations"""
        print("\n💡 RECOMMANDATIONS:")
        
        if not self.results.get("ping", False):
            print("   • Vérifiez le câble RJ45")
            print("   • Vérifiez que l'IC-R8600 est allumé")
            print("   • Vérifiez la configuration IP de l'IC-R8600")
        
        if not self.results.get("port_scan", False):
            print("   • Activez 'Remote Control' sur l'IC-R8600")
            print("   • Vérifiez les paramètres réseau de l'IC-R8600")
        
        if not self.results.get("udp_communication", False):
            print("   • Vérifiez l'adresse CI-V (0x96 pour IC-R8600)")
            print("   • Activez 'CI-V over LAN' sur l'IC-R8600")

def main():
    """Fonction principale"""
    tester = NetworkTester()
    
    print("🚀 Démarrage du test automatique...")
    success = tester.run_full_test()
    
    if success:
        print("\n✅ Tests réussis - Vous pouvez démarrer le serveur!")
        sys.exit(0)
    else:
        print("\n❌ Tests échoués - Corrigez les problèmes avant de continuer")
        sys.exit(1)

if __name__ == "__main__":
    main()

